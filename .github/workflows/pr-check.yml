name: PR Checks
on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  template-check:
    runs-on: ubuntu-latest
    steps:
      - name: Check PR body
        run: |
          if [[ "${{ github.event.pull_request.body }}" == *"## Description"* ]]; then
            echo "PR template used ✓"
          else
            echo "Please use the PR template"
            exit 1
          fi

  lint-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Check build
        run: npm run build
