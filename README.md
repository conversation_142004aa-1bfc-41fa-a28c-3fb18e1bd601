# Bomberman DOM Game

A modular Bomberman game built with pure JavaScript, DOM elements, and CSS using a custom mini-framework.

## 🏗️ Architecture Overview

The codebase is organized into modular components for better maintainability, testing, and scalability.

### 📁 Project Structure

```
Bomberman-DOM/
├── index.html                 # Entry point
├── src/
│   ├── Game-modular.js        # Main game orchestrator
│   ├── core/                  # Core game systems
│   │   ├── GameLoop.js        # requestAnimationFrame game loop
│   │   ├── GameState.js       # Global state management
│   │   └── InputManager.js    # Keyboard/mouse input handling
│   ├── systems/               # Game logic systems
│   │   └── BombSystem.js      # Bomb placement, explosions, chain reactions
│   ├── components/            # UI/Game components
│   │   ├── Board.js           # Game board and tile management
│   │   └── Tile.js            # Individual tile component (legacy)
│   └── styles/                # Modular CSS
│       ├── main.css           # CSS entry point (imports all)
│       ├── base.css           # Reset, variables, typography
│       ├── layout.css         # Page layout, containers
│       ├── components.css     # UI components (buttons, cards)
│       ├── board.css          # Game board and tiles
│       ├── game-elements.css  # Players, bombs, explosions
│       └── animations.css     # All CSS animations
└── mini-framework/            # Custom DOM framework (submodule)
```

## 🎮 Features

### ✅ Implemented
- **Modular Architecture**: Clean separation of concerns
- **Game Loop**: 60 FPS rendering with 30 TPS game logic
- **Maze Generation**: Classic Bomberman wall patterns
- **Bomb System**: Placement, countdown, explosions, chain reactions
- **Rendering System**: Pure DOM elements with CSS animations
- **Input Management**: Keyboard controls with customizable bindings
- **State Management**: Centralized game state with reactive updates
- **Responsive Design**: Works on desktop, tablet, and mobile

### 🎨 Visual Elements
- **Players**: 4 different colored players with directional indicators
- **Bombs**: Animated bombs with countdown timers and fuse effects
- **Explosions**: Dynamic explosions with flames and sparks
- **Pickups**: Power-ups with floating and glowing animations
- **Walls**: Destructible and indestructible walls with textures

## 🚀 Getting Started

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Bomberman-DOM
   ```

2. **Initialize submodules**:
   ```bash
   git submodule update --init --recursive
   ```

3. **Start a local server**:
   ```bash
   python3 -m http.server 8000
   # or
   npx serve .
   ```

4. **Open in browser**:
   ```
   http://localhost:8000
   ```

## 🎯 Controls

- **Arrow Keys / WASD**: Move Player 1
- **Space / Q**: Place Bomb
- **Esc / P**: Pause/Resume
- **R**: Reset Game
- **Mouse**: Click tiles for interaction

## 🔧 Development

### Adding New Features

1. **Core Systems**: Add to `src/core/` for fundamental game mechanics
2. **Game Systems**: Add to `src/systems/` for specific game logic (AI, physics, etc.)
3. **Components**: Add to `src/components/` for UI or game objects
4. **Styles**: Add to appropriate CSS file in `src/styles/`

### CSS Architecture

The CSS follows a modular approach:

- **base.css**: Foundation (reset, variables, typography)
- **layout.css**: Page structure and responsive design
- **components.css**: Reusable UI components
- **board.css**: Game board specific styles
- **game-elements.css**: Game object rendering
- **animations.css**: All keyframe animations

### JavaScript Modules

- **GameLoop**: Handles requestAnimationFrame and timing
- **GameState**: Manages global state and game data
- **InputManager**: Processes keyboard/mouse input
- **BombSystem**: Handles bomb logic and explosions
- **Board**: Manages the game board and tile rendering

## 🎮 Game Systems

### Game Loop
- **Fixed Timestep**: Game logic runs at 30 TPS for consistency
- **Variable Timestep**: Rendering at 60 FPS for smooth animations
- **Performance Monitoring**: Internal FPS tracking

### Bomb System
- **Placement**: Validates position and player bomb limits
- **Countdown**: Real-time timer with visual feedback
- **Explosions**: Cross-pattern with configurable power
- **Chain Reactions**: Bombs trigger other bombs
- **Wall Destruction**: Destroys destructible walls
- **Pickup Spawning**: Random power-ups from destroyed walls

### Input System
- **Customizable Bindings**: Remap keys for different players
- **Action-Based**: Maps keys to game actions
- **Multi-Player Support**: Separate controls for 4 players
- **Event Handling**: Clean callback system

## 🧪 Testing

Use the built-in test buttons:
- **"Add Test Elements"**: Places sample game objects
- **"Clear Elements"**: Removes all game objects
- **Space Key**: Places bombs for testing explosions

## 📱 Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **ES6 Modules**: Required for imports
- **CSS Grid**: Used for board layout
- **requestAnimationFrame**: For smooth animations

## 🔮 Future Enhancements

- **Player Movement**: Smooth character movement
- **Multiplayer**: Local and network multiplayer
- **AI Enemies**: Computer-controlled opponents
- **Sound System**: Audio effects and music
- **Level Editor**: Create custom maps
- **Power-ups**: More pickup types and effects
- **Particle System**: Enhanced visual effects

Built with ❤️ using pure JavaScript and DOM manipulation.