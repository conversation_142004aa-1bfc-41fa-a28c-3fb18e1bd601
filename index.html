<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Bomberman game built with custom JavaScript DOM framework">
    <meta name="keywords" content="bomberman, game, javascript, dom, framework">
    <meta name="author" content="Bomberman DOM Game">
    
    <title>Bomberman - DOM Game</title>
    
    <!-- Game Styles -->
    <link rel="stylesheet" href="src/styles/main.css">
    
    <!-- Favicon (optional) -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💣</text></svg>">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="mini-framework/src/framework.js" as="script">
    <link rel="preload" href="src/Game.js" as="script">
</head>
<body>
    <!-- Loading screen -->
    <div id="loading-screen" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: Arial, sans-serif;
        z-index: 1000;
    ">
        <div style="font-size: 4rem; margin-bottom: 20px;">💣</div>
        <h1 style="font-size: 2.5rem; margin-bottom: 10px;">Bomberman</h1>
        <p style="font-size: 1.2rem; opacity: 0.8;">Loading game...</p>
        <div style="
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        ">
            <div id="loading-bar" style="
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #27ae60, #2ecc71);
                border-radius: 2px;
                transition: width 0.3s ease;
            "></div>
        </div>
    </div>

    <!-- Main game container -->
    <div id="app" style="display: none;">
        <!-- Game will be mounted here -->
    </div>

    <!-- Error fallback -->
    <div id="error-screen" style="display: none;">
        <div style="
            background: rgba(231, 76, 60, 0.1);
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
            text-align: center;
            color: #e74c3c;
        ">
            <h2>⚠️ Game Loading Error</h2>
            <p>Sorry, there was an error loading the Bomberman game.</p>
            <p>Please check the console for more details and try refreshing the page.</p>
            <button onclick="location.reload()" style="
                background: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">Reload Game</button>
        </div>
    </div>

    <!-- Game initialization script -->
    <script type="module">
        // Loading progress simulation
        let progress = 0;
        const loadingBar = document.getElementById('loading-bar');
        const loadingInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            loadingBar.style.width = progress + '%';
        }, 100);

        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Game error:', event.error);
            showError();
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            showError();
        });

        function showError() {
            document.getElementById('loading-screen').style.display = 'none';
            document.getElementById('app').style.display = 'none';
            document.getElementById('error-screen').style.display = 'block';
            clearInterval(loadingInterval);
        }

        function hideLoading() {
            clearInterval(loadingInterval);
            loadingBar.style.width = '100%';
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('app').style.display = 'block';
            }, 500);
        }

        // Import and initialize the game
        async function initializeGame() {
            try {
                console.log('Starting Bomberman game initialization...');

                // Import the modular Game class
                const { default: Game } = await import('./src/Game-modular.js');
                
                console.log('Game module loaded successfully');

                // Get the app mount point
                const appElement = document.getElementById('app');
                if (!appElement) {
                    throw new Error('App mount element not found');
                }

                // Create and initialize the game
                const game = new Game();
                await game.init(appElement);

                console.log('Game initialized successfully');

                // Hide loading screen and show game
                hideLoading();

                // Store game instance globally for debugging
                window.bombermanGame = game;

                // Log success message
                console.log('🎮 Bomberman game is ready to play!');
                console.log('Use arrow keys or WASD to move (when player is implemented)');
                console.log('Press Space to place bombs (when implemented)');
                console.log('Press Esc to pause/resume');

            } catch (error) {
                console.error('Failed to initialize game:', error);
                showError();
            }
        }

        // Start the game when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeGame);
        } else {
            initializeGame();
        }

        // Expose game utilities for debugging
        window.gameDebug = {
            getGlobalState: () => {
                return window.globalState ? window.globalState.getAll() : null;
            },
            getBoardState: () => {
                return window.globalState ? window.globalState.get('board.state') : null;
            },
            resetGame: () => {
                if (window.bombermanGame) {
                    const gameComponent = window.bombermanGame.getGameComponent();
                    if (gameComponent) {
                        gameComponent.resetGame();
                    }
                }
            }
        };
    </script>

    <!-- Development helpers (remove in production) -->
    <script>
        // Console styling for development
        if (typeof console !== 'undefined') {
            console.log('%c🎮 Bomberman DOM Game', 'color: #27ae60; font-size: 16px; font-weight: bold;');
            console.log('%cBuilt with custom JavaScript DOM framework', 'color: #3498db; font-size: 12px;');
            console.log('%cOpen browser dev tools to see game state and debug info', 'color: #95a5a6; font-size: 10px;');
        }

        // Performance monitoring
        if (typeof performance !== 'undefined') {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log(`⚡ Page loaded in ${Math.round(perfData.loadEventEnd - perfData.fetchStart)}ms`);
                }, 0);
            });
        }
    </script>
</body>
</html>
