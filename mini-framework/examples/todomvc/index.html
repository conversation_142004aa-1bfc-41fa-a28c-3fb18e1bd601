<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TodoMVC - Mini Framework</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <section class="todoapp">
        <header class="header">
            <h1>todos</h1>
            <input class="new-todo" placeholder="What needs to be done?" autofocus>
        </header>
        <section id="main" class="main">
            <input id="toggle-all" class="toggle-all" type="checkbox">
            <label for="toggle-all">Mark all as complete</label>
            <ul id="todo-list" class="todo-list"></ul>
        </section>
        <footer id="footer" class="footer">
            <span class="todo-count"><strong>0</strong> items left</span>
            <ul class="filters">
                <li><a href="#/" class="selected">All</a></li>
                <li><a href="#/active">Active</a></li>
                <li><a href="#/completed">Completed</a></li>
            </ul>
            <button class="clear-completed">Clear completed</button>
        </footer>
    </section>
    
    <footer class="info">
        <p>Double-click to edit a todo</p>
        <p>Created with <strong>Mini Framework</strong></p>
        <p>A zero-dependency JavaScript framework</p>
    </footer>

    <script type="module" src="app.js"></script>
</body>
</html>
