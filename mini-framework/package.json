{"name": "@bernotieno/mini-framework", "version": "1.1.0", "description": "A lightweight JavaScript framework built from scratch with zero dependencies", "main": "src/framework.js", "module": "src/framework.js", "type": "module", "exports": {".": {"import": "./src/framework.js", "types": "./types/index.d.ts"}, "./core/*": {"import": "./src/core/*.js"}, "./utils": {"import": "./src/utils.js"}}, "files": ["src/", "types/", "README.md", "LICENSE"], "scripts": {"dev": "npx http-server -p 3000 -c-1", "start": "npm run dev", "test": "echo \"Tests coming soon\" && exit 0", "build": "echo \"No build step required - pure ES modules\" && exit 0", "prepublishOnly": "npm test", "version": "npm run build && git add -A src", "postversion": "git push && git push --tags"}, "keywords": ["javascript", "framework", "virtual-dom", "state-management", "routing", "zero-dependencies", "es-modules", "reactive", "component-system", "spa", "frontend"], "author": "Mini Framework Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/bernotieno/mini-framework.git"}, "bugs": {"url": "https://github.com/bernotieno/mini-framework/issues"}, "homepage": "https://github.com/bernotieno/mini-framework#readme", "engines": {"node": ">=14.0.0"}, "devDependencies": {"http-server": "^14.1.1"}, "dependencies": {}}