/**
 * Main Game Module (Modular Version)
 * 
 * Simplified main game class that orchestrates all the modular components.
 * This replaces the monolithic Game.js with a cleaner, more maintainable structure.
 */

import { createApp, defineComponent, h } from '../mini-framework/src/framework.js';
import { Board } from './components/Board.js';
import { GameLoop } from './core/GameLoop.js';
import { GameState } from './core/GameState.js';
import { InputManager } from './core/InputManager.js';
import { BombSystem } from './systems/BombSystem.js';

/**
 * Main Game Application Component
 */
const GameApp = defineComponent({
    /**
     * Initialize game application
     */
    afterMount() {
        console.log('Bomberman Game Initialized');
        
        // Initialize core systems
        this.initializeSystems();
        
        // Set up input handling
        this.setupInputHandlers();
        
        // Mount the board component
        this.mountBoardComponent();
        
        // Start the game loop
        this.gameLoop.start();
    },

    /**
     * Initialize all game systems
     */
    initializeSystems() {
        // Initialize game state
        this.gameState = new GameState();
        this.gameState.initialize();
        
        // Initialize game loop
        this.gameLoop = new GameLoop();
        this.gameLoop.setUpdateCallback((deltaTime) => this.updateGame(deltaTime));
        this.gameLoop.setRenderCallback((deltaTime) => this.renderGame(deltaTime));
        
        // Initialize input manager
        this.inputManager = new InputManager();
        
        console.log('Core systems initialized');
    },

    /**
     * Setup input handlers
     */
    setupInputHandlers() {
        // Game control handlers
        this.inputManager.onAction('pause', () => this.togglePause());
        this.inputManager.onAction('start', () => this.startGame());
        this.inputManager.onAction('reset', () => this.resetGame());
        
        // Player movement handlers (placeholder for future implementation)
        this.inputManager.onAction('player1_up', () => this.handlePlayerMove(1, 'up'));
        this.inputManager.onAction('player1_down', () => this.handlePlayerMove(1, 'down'));
        this.inputManager.onAction('player1_left', () => this.handlePlayerMove(1, 'left'));
        this.inputManager.onAction('player1_right', () => this.handlePlayerMove(1, 'right'));
        this.inputManager.onAction('player1_bomb', () => this.handlePlayerBomb(1));
        
        console.log('Input handlers setup complete');
    },

    /**
     * Mount the board component
     */
    mountBoardComponent() {
        const boardContainer = document.getElementById('board-container');
        if (boardContainer) {
            this.boardComponent = new Board();
            this.boardComponent.mount(boardContainer);
            
            // Initialize bomb system with board reference
            this.bombSystem = new BombSystem(this.boardComponent, this.gameState);
            
            console.log('Board component and bomb system initialized');
        } else {
            console.error('Board container not found');
        }
    },

    /**
     * Update game logic (called from game loop)
     */
    updateGame(deltaTime) {
        if (!this.gameState.isPlaying()) return;

        // Update game timer
        const gameEnded = this.gameState.updateTimer(deltaTime);
        if (gameEnded) {
            console.log('Game ended - time up');
            return;
        }

        // Update bomb system
        if (this.bombSystem) {
            const bombsChanged = this.bombSystem.updateBombs(deltaTime);
            const explosionsChanged = this.bombSystem.updateExplosions(deltaTime);
            
            if (bombsChanged || explosionsChanged) {
                this.needsRerender = true;
            }
        }
    },

    /**
     * Render game frame (called from game loop)
     */
    renderGame(deltaTime) {
        // Trigger board re-render if needed
        if (this.needsRerender && this.boardComponent) {
            this.boardComponent.setState('frameUpdate', Date.now());
            this.needsRerender = false;
        }
    },

    /**
     * Handle player movement input
     */
    handlePlayerMove(playerId, direction) {
        console.log(`Player ${playerId} move ${direction}`);
        // Future: Implement player movement
    },

    /**
     * Handle player bomb placement
     */
    handlePlayerBomb(playerId) {
        if (!this.gameState.isPlaying() || !this.bombSystem) return;
        
        // For demo: place bomb at player 1 spawn position
        const success = this.bombSystem.placeBomb(1, 1, playerId, 2, 3000);
        if (success) {
            this.needsRerender = true;
        }
    },

    /**
     * Start the game
     */
    startGame() {
        this.gameState.setStatus('playing');
        console.log('Game started!');
    },

    /**
     * Toggle game pause state
     */
    togglePause() {
        if (this.gameState.isPlaying()) {
            this.gameState.setStatus('paused');
        } else if (this.gameState.isPaused()) {
            this.gameState.setStatus('playing');
        }
    },

    /**
     * Reset the game
     */
    resetGame() {
        this.gameState.reset();
        
        if (this.boardComponent) {
            this.boardComponent.resetBoard();
            this.boardComponent.clearGameElements();
        }
        
        if (this.bombSystem) {
            this.bombSystem.clearAllBombs();
        }
        
        console.log('Game reset!');
    },

    /**
     * Add test elements for demonstration
     */
    addTestElements() {
        if (this.boardComponent) {
            this.boardComponent.addTestElements();
        }
    },

    /**
     * Clear test elements
     */
    clearTestElements() {
        if (this.boardComponent) {
            this.boardComponent.clearGameElements();
        }
    },

    /**
     * Render the main game interface
     */
    render() {
        const gameStatus = this.gameState ? this.gameState.getStatus() : 'initialized';
        
        return h('div', { class: 'game-container' }, [
            // Game header
            h('header', { class: 'game-header' }, [
                h('h1', { class: 'game-title' }, 'Bomberman'),
                h('div', { class: 'game-controls' }, [
                    h('button', {
                        class: 'btn btn-success',
                        onclick: () => this.startGame(),
                        disabled: gameStatus === 'playing'
                    }, gameStatus === 'playing' ? 'Playing' : 'Start Game'),
                    h('button', {
                        class: 'btn btn-warning',
                        onclick: () => this.togglePause(),
                        disabled: gameStatus === 'initialized'
                    }, gameStatus === 'paused' ? 'Resume' : 'Pause'),
                    h('button', {
                        class: 'btn btn-secondary',
                        onclick: () => this.resetGame()
                    }, 'Reset'),
                    h('button', {
                        class: 'btn btn-info',
                        onclick: () => this.addTestElements()
                    }, 'Add Test Elements'),
                    h('button', {
                        class: 'btn btn-gray',
                        onclick: () => this.clearTestElements()
                    }, 'Clear Elements')
                ])
            ]),

            // Game info panel
            h('div', { class: 'game-info' }, [
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Status: '),
                    h('span', { class: 'info-value' }, gameStatus)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Score: '),
                    h('span', { class: 'info-value' }, this.gameState ? this.gameState.getScore() : 0)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Time: '),
                    h('span', { class: 'info-value' }, Math.ceil(this.gameState ? this.gameState.getTimeRemaining() : 300))
                ])
            ]),

            // Main game board
            h('main', { class: 'game-main' }, [
                h('div', { id: 'board-container', class: 'board-container' }, [])
            ]),

            // Game footer
            h('footer', { class: 'game-footer' }, [
                h('div', { class: 'instructions' }, [
                    h('h3', {}, 'Controls:'),
                    h('p', {}, 'Arrow Keys or WASD: Move Player 1'),
                    h('p', {}, 'Space or Q: Place Bomb'),
                    h('p', {}, 'Esc or P: Pause/Resume'),
                    h('p', {}, 'R: Reset Game')
                ])
            ])
        ]);
    }
});

/**
 * Simplified Game class
 */
export class Game {
    constructor() {
        this.app = null;
        this.gameComponent = null;
    }

    /**
     * Initialize and start the game application
     */
    init(mountElement) {
        if (!mountElement) {
            throw new Error('Mount element is required to initialize the game');
        }

        try {
            // Create the mini-framework app
            this.app = createApp();

            // Register components
            this.app.component('GameApp', GameApp);
            this.app.component('Board', Board);

            // Create and mount the main game component
            this.gameComponent = new GameApp();
            this.gameComponent.mount(mountElement);

            console.log('Modular Bomberman game successfully initialized');
            
            return this;
        } catch (error) {
            console.error('Failed to initialize game:', error);
            throw error;
        }
    }

    /**
     * Get the current game instance
     */
    getGameComponent() {
        return this.gameComponent;
    }

    /**
     * Destroy the game and clean up resources
     */
    destroy() {
        if (this.gameComponent) {
            // Clean up systems
            if (this.gameComponent.gameLoop) {
                this.gameComponent.gameLoop.stop();
            }
            if (this.gameComponent.inputManager) {
                this.gameComponent.inputManager.destroy();
            }
            
            this.gameComponent = null;
        }
        this.app = null;
        console.log('Game destroyed');
    }
}

export default Game;
