/**
 * Game Module
 * 
 * Main game initialization and management module for Bomberman.
 * Handles app creation, component registration, and game state management.
 * 
 * Features:
 * - Game initialization and setup
 * - Component registration and mounting
 * - Global game state management
 * - Event system setup
 * - Future: Game loop, player management, collision detection
 */

import { createApp, defineComponent, h, globalState, eventEmitter } from '../mini-framework/src/framework.js';
import { Board } from './components/Board.js';

/**
 * Main Game Application Component
 */
const GameApp = defineComponent({

    /**
     * Initialize global game state
     */
    initializeGameState() {
        // Game configuration
        globalState.set('game', {
            status: 'initialized', // initialized, playing, paused, gameOver
            level: 1,
            score: 0,
            lives: 3,
            timeRemaining: 300, // 5 minutes in seconds
            players: [],
            enemies: [],
            bombs: [],
            explosions: [],
            pickups: []
        });

        // Game settings
        globalState.set('settings', {
            soundEnabled: true,
            musicEnabled: true,
            difficulty: 'normal', // easy, normal, hard
            playerCount: 1 // 1-4 players
        });

        console.log('Game state initialized');
    },

    /**
     * Set up event listeners for game interactions
     */
    setupEventListeners() {
        // Listen for tile clicks (for debugging and future interactions)
        eventEmitter.on('tile-clicked', (tileData) => {
            console.log('Tile clicked:', tileData);
            // Future: Handle tile interactions (bomb placement, etc.)
        });

        // Listen for keyboard events (for future player controls)
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });

        // Listen for window resize (for responsive design)
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    },

    /**
     * Handle keyboard input (for future player controls)
     */
    handleKeyDown(event) {
        // Prevent default browser behavior for game keys
        const gameKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space', 'Enter'];
        if (gameKeys.includes(event.code)) {
            event.preventDefault();
        }

        // Future: Implement player movement and actions
        switch (event.code) {
            case 'ArrowUp':
            case 'KeyW':
                console.log('Move up');
                break;
            case 'ArrowDown':
            case 'KeyS':
                console.log('Move down');
                break;
            case 'ArrowLeft':
            case 'KeyA':
                console.log('Move left');
                break;
            case 'ArrowRight':
            case 'KeyD':
                console.log('Move right');
                break;
            case 'Space':
                console.log('Place bomb');
                break;
            case 'Escape':
                this.togglePause();
                break;
        }
    },

    /**
     * Handle key release events
     */
    handleKeyUp(event) {
        // Future: Handle key release for smooth movement
    },

    /**
     * Handle window resize
     */
    handleResize() {
        // Future: Adjust game layout for different screen sizes
        console.log('Window resized');
    },

    /**
     * Subscribe to game state changes
     */
    subscribeToGameState() {
        // Listen for game status changes
        globalState.subscribe('game.status', (status) => {
            console.log('Game status changed to:', status);
            this.setState('updated', Date.now());
        });

        // Listen for score changes
        globalState.subscribe('game.score', (score) => {
            console.log('Score updated:', score);
        });
    },

    /**
     * Toggle game pause state
     */
    togglePause() {
        const currentStatus = globalState.get('game.status');
        if (currentStatus === 'playing') {
            globalState.set('game.status', 'paused');
        } else if (currentStatus === 'paused') {
            globalState.set('game.status', 'playing');
        }
    },

    /**
     * Start the game
     */
    startGame() {
        globalState.set('game.status', 'playing');
        console.log('Game started!');
    },

    /**
     * Reset the game
     */
    resetGame() {
        this.initializeGameState();
        // Reset board through board component
        const boardComponent = globalState.get('boardComponent');
        if (boardComponent) {
            boardComponent.resetBoard();
        }
        console.log('Game reset!');
    },

    /**
     * Render the main game interface
     */
    render() {
        const gameStatus = globalState.get('game.status') || 'initialized';

        return h('div', { class: 'game-container' }, [
            // Game header with title and controls
            h('header', { class: 'game-header' }, [
                h('h1', { class: 'game-title' }, 'Bomberman'),
                h('div', { class: 'game-controls' }, [
                    h('button', {
                        class: 'btn btn-start',
                        onclick: () => this.startGame(),
                        disabled: gameStatus === 'playing'
                    }, gameStatus === 'playing' ? 'Playing' : 'Start Game'),
                    h('button', {
                        class: 'btn btn-reset',
                        onclick: () => this.resetGame()
                    }, 'Reset'),
                    h('button', {
                        class: 'btn btn-pause',
                        onclick: () => this.togglePause(),
                        disabled: gameStatus === 'initialized'
                    }, gameStatus === 'paused' ? 'Resume' : 'Pause')
                ])
            ]),

            // Game info panel
            h('div', { class: 'game-info' }, [
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Status: '),
                    h('span', { class: 'info-value' }, gameStatus)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Score: '),
                    h('span', { class: 'info-value' }, globalState.get('game.score') || 0)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Level: '),
                    h('span', { class: 'info-value' }, globalState.get('game.level') || 1)
                ])
            ]),

            // Main game board - create a container for the board component
            h('main', { class: 'game-main' }, [
                h('div', { id: 'board-container', class: 'board-container' }, [])
            ]),

            // Game footer with instructions
            h('footer', { class: 'game-footer' }, [
                h('div', { class: 'instructions' }, [
                    h('h3', {}, 'Controls:'),
                    h('p', {}, 'Arrow Keys or WASD: Move'),
                    h('p', {}, 'Space: Place Bomb'),
                    h('p', {}, 'Esc: Pause/Resume')
                ])
            ])
        ]);
    },

    /**
     * After the main component is mounted, mount the board component
     */
    afterMount() {
        console.log('Bomberman Game Initialized');

        // Set up global game state
        this.initializeGameState();

        // Set up event listeners for game interactions
        this.setupEventListeners();

        // Subscribe to game state changes
        this.subscribeToGameState();

        // Mount the board component after the main component is rendered
        this.mountBoardComponent();
    },

    /**
     * Mount the board component to its container
     */
    mountBoardComponent() {
        const boardContainer = document.getElementById('board-container');
        if (boardContainer) {
            this.boardComponent = new Board();
            this.boardComponent.mount(boardContainer);
            console.log('Board component mounted successfully');
        } else {
            console.error('Board container not found');
        }
    }
});

/**
 * Game class for managing the application lifecycle
 */
export class Game {
    constructor() {
        this.app = null;
        this.gameComponent = null;
    }

    /**
     * Initialize and start the game application
     */
    init(mountElement) {
        if (!mountElement) {
            throw new Error('Mount element is required to initialize the game');
        }

        try {
            // Create the mini-framework app
            this.app = createApp();

            // Register components
            this.app.component('GameApp', GameApp);
            this.app.component('Board', Board);

            // Create and mount the main game component
            this.gameComponent = new GameApp();
            this.gameComponent.mount(mountElement);

            console.log('Bomberman game successfully initialized and mounted');
            
            return this;
        } catch (error) {
            console.error('Failed to initialize game:', error);
            throw error;
        }
    }

    /**
     * Get the current game instance
     */
    getGameComponent() {
        return this.gameComponent;
    }

    /**
     * Destroy the game and clean up resources
     */
    destroy() {
        if (this.gameComponent) {
            // Future: Add cleanup logic
            this.gameComponent = null;
        }
        this.app = null;
        console.log('Game destroyed');
    }
}

// Export the Game class as default
export default Game;
