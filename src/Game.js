/**
 * Game Module
 * 
 * Main game initialization and management module for Bomberman.
 * Handles app creation, component registration, and game state management.
 * 
 * Features:
 * - Game initialization and setup
 * - Component registration and mounting
 * - Global game state management
 * - Event system setup
 * - Future: Game loop, player management, collision detection
 */

import { createApp, defineComponent, h, globalState, eventEmitter } from '../mini-framework/src/framework.js';
import { Board } from './components/Board.js';

/**
 * Main Game Application Component
 */
const GameApp = defineComponent({

    /**
     * Initialize global game state
     */
    initializeGameState() {
        // Game configuration
        globalState.set('game', {
            status: 'initialized', // initialized, playing, paused, gameOver
            level: 1,
            score: 0,
            lives: 3,
            timeRemaining: 300, // 5 minutes in seconds
            players: [],
            enemies: [],
            bombs: [],
            explosions: [],
            pickups: []
        });

        // Game settings
        globalState.set('settings', {
            soundEnabled: true,
            musicEnabled: true,
            difficulty: 'normal', // easy, normal, hard
            playerCount: 1 // 1-4 players
        });

        console.log('Game state initialized');
    },

    /**
     * Set up event listeners for game interactions
     */
    setupEventListeners() {
        // Listen for tile clicks (for debugging and future interactions)
        eventEmitter.on('tile-clicked', (tileData) => {
            console.log('Tile clicked:', tileData);
            // Future: Handle tile interactions (bomb placement, etc.)
        });

        // Listen for keyboard events (for future player controls)
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });

        // Listen for window resize (for responsive design)
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    },

    /**
     * Handle keyboard input (for future player controls)
     */
    handleKeyDown(event) {
        // Prevent default browser behavior for game keys
        const gameKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space', 'Enter'];
        if (gameKeys.includes(event.code)) {
            event.preventDefault();
        }

        // Future: Implement player movement and actions
        switch (event.code) {
            case 'ArrowUp':
            case 'KeyW':
                console.log('Move up');
                break;
            case 'ArrowDown':
            case 'KeyS':
                console.log('Move down');
                break;
            case 'ArrowLeft':
            case 'KeyA':
                console.log('Move left');
                break;
            case 'ArrowRight':
            case 'KeyD':
                console.log('Move right');
                break;
            case 'Space':
                console.log('Place bomb');
                break;
            case 'Escape':
                this.togglePause();
                break;
        }
    },

    /**
     * Handle key release events
     */
    handleKeyUp(event) {
        // Future: Handle key release for smooth movement
    },

    /**
     * Handle window resize
     */
    handleResize() {
        // Future: Adjust game layout for different screen sizes
        console.log('Window resized');
    },

    /**
     * Subscribe to game state changes
     */
    subscribeToGameState() {
        // Listen for game status changes
        globalState.subscribe('game.status', (status) => {
            console.log('Game status changed to:', status);
            this.setState('updated', Date.now());
        });

        // Listen for score changes
        globalState.subscribe('game.score', (score) => {
            console.log('Score updated:', score);
        });
    },

    /**
     * Toggle game pause state
     */
    togglePause() {
        const currentStatus = globalState.get('game.status');
        if (currentStatus === 'playing') {
            globalState.set('game.status', 'paused');
        } else if (currentStatus === 'paused') {
            globalState.set('game.status', 'playing');
        }
    },

    /**
     * Start the game
     */
    startGame() {
        globalState.set('game.status', 'playing');

        // Start the game loop if not already running
        if (!this.gameLoop.isRunning) {
            this.startGameLoop();
        }

        console.log('Game started!');
    },

    /**
     * Reset the game
     */
    resetGame() {
        // Stop the game loop
        this.stopGameLoop();

        // Reset game state
        this.initializeGameState();

        // Reset board through board component
        const boardComponent = globalState.get('boardComponent');
        if (boardComponent) {
            boardComponent.resetBoard();
            boardComponent.clearGameElements();
        }

        // Restart the game loop
        this.startGameLoop();

        console.log('Game reset!');
    },

    /**
     * Add test elements to demonstrate the rendering system
     */
    addTestElements() {
        const boardComponent = globalState.get('boardComponent');
        if (boardComponent) {
            boardComponent.addTestElements();
        } else {
            console.error('Board component not found');
        }
    },

    /**
     * Clear all test elements from the board
     */
    clearTestElements() {
        const boardComponent = globalState.get('boardComponent');
        if (boardComponent) {
            boardComponent.clearGameElements();
        } else {
            console.error('Board component not found');
        }
    },

    /**
     * Render the main game interface
     */
    render() {
        const gameStatus = globalState.get('game.status') || 'initialized';

        return h('div', { class: 'game-container' }, [
            // Game header with title and controls
            h('header', { class: 'game-header' }, [
                h('h1', { class: 'game-title' }, 'Bomberman'),
                h('div', { class: 'game-controls' }, [
                    h('button', {
                        class: 'btn btn-start',
                        onclick: () => this.startGame(),
                        disabled: gameStatus === 'playing'
                    }, gameStatus === 'playing' ? 'Playing' : 'Start Game'),
                    h('button', {
                        class: 'btn btn-reset',
                        onclick: () => this.resetGame()
                    }, 'Reset'),
                    h('button', {
                        class: 'btn btn-pause',
                        onclick: () => this.togglePause(),
                        disabled: gameStatus === 'initialized'
                    }, gameStatus === 'paused' ? 'Resume' : 'Pause'),
                    h('button', {
                        class: 'btn btn-test',
                        onclick: () => this.addTestElements()
                    }, 'Add Test Elements'),
                    h('button', {
                        class: 'btn btn-clear',
                        onclick: () => this.clearTestElements()
                    }, 'Clear Elements')
                ])
            ]),

            // Game info panel
            h('div', { class: 'game-info' }, [
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Status: '),
                    h('span', { class: 'info-value' }, gameStatus)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Score: '),
                    h('span', { class: 'info-value' }, globalState.get('game.score') || 0)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Level: '),
                    h('span', { class: 'info-value' }, globalState.get('game.level') || 1)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'FPS: '),
                    h('span', { class: 'info-value fps-counter' }, globalState.get('game.fps') || 0)
                ]),
                h('div', { class: 'info-item' }, [
                    h('span', { class: 'info-label' }, 'Time: '),
                    h('span', { class: 'info-value timer' }, Math.ceil(globalState.get('game.timeRemaining') || 300))
                ])
            ]),

            // Main game board - create a container for the board component
            h('main', { class: 'game-main' }, [
                h('div', { id: 'board-container', class: 'board-container' }, [])
            ]),

            // Game footer with instructions
            h('footer', { class: 'game-footer' }, [
                h('div', { class: 'instructions' }, [
                    h('h3', {}, 'Controls:'),
                    h('p', {}, 'Arrow Keys or WASD: Move'),
                    h('p', {}, 'Space: Place Bomb'),
                    h('p', {}, 'Esc: Pause/Resume')
                ])
            ])
        ]);
    },

    /**
     * After the main component is mounted, mount the board component
     */
    afterMount() {
        console.log('Bomberman Game Initialized');

        // Set up global game state
        this.initializeGameState();

        // Set up event listeners for game interactions
        this.setupEventListeners();

        // Subscribe to game state changes
        this.subscribeToGameState();

        // Mount the board component after the main component is rendered
        this.mountBoardComponent();

        // Initialize the game loop
        this.initializeGameLoop();
    },

    /**
     * Initialize the main game loop with requestAnimationFrame
     */
    initializeGameLoop() {
        this.gameLoop = {
            isRunning: false,
            lastTime: 0,
            deltaTime: 0,
            targetFPS: 60,
            frameTime: 1000 / 60, // 16.67ms per frame
            accumulator: 0,
            animationId: null,

            // Game timing
            gameTickRate: 1000 / 30, // 30 game ticks per second
            lastGameTick: 0,

            // Performance tracking
            frameCount: 0,
            fpsCounter: 0,
            lastFPSUpdate: 0
        };

        // Start the game loop
        this.startGameLoop();
        console.log('Game loop initialized with 60 FPS target');
    },

    /**
     * Start the main game loop
     */
    startGameLoop() {
        if (this.gameLoop.isRunning) return;

        this.gameLoop.isRunning = true;
        this.gameLoop.lastTime = performance.now();
        this.gameLoop.lastGameTick = this.gameLoop.lastTime;
        this.gameLoop.lastFPSUpdate = this.gameLoop.lastTime;

        // Start the loop
        this.runGameLoop();
        console.log('Game loop started');
    },

    /**
     * Stop the main game loop
     */
    stopGameLoop() {
        if (!this.gameLoop.isRunning) return;

        this.gameLoop.isRunning = false;
        if (this.gameLoop.animationId) {
            cancelAnimationFrame(this.gameLoop.animationId);
            this.gameLoop.animationId = null;
        }
        console.log('Game loop stopped');
    },

    /**
     * Main game loop using requestAnimationFrame
     */
    runGameLoop() {
        if (!this.gameLoop.isRunning) return;

        const currentTime = performance.now();
        this.gameLoop.deltaTime = currentTime - this.gameLoop.lastTime;
        this.gameLoop.lastTime = currentTime;

        // Add to accumulator for fixed timestep updates
        this.gameLoop.accumulator += this.gameLoop.deltaTime;

        // Fixed timestep game logic updates
        while (this.gameLoop.accumulator >= this.gameLoop.gameTickRate) {
            this.updateGameLogic(this.gameLoop.gameTickRate);
            this.gameLoop.accumulator -= this.gameLoop.gameTickRate;
            this.gameLoop.lastGameTick = currentTime;
        }

        // Variable timestep rendering
        this.renderFrame(this.gameLoop.deltaTime);

        // Update performance counters
        this.updatePerformanceCounters(currentTime);

        // Schedule next frame
        this.gameLoop.animationId = requestAnimationFrame(() => this.runGameLoop());
    },

    /**
     * Update game logic at fixed timestep
     */
    updateGameLogic(deltaTime) {
        const gameStatus = globalState.get('game.status');

        // Only update game logic when playing
        if (gameStatus !== 'playing') return;

        // Update game timer
        const timeRemaining = globalState.get('game.timeRemaining') || 300;
        const newTime = Math.max(0, timeRemaining - (deltaTime / 1000));
        globalState.set('game.timeRemaining', newTime);

        // Update bombs (countdown timers)
        this.updateBombs(deltaTime);

        // Update explosions (duration)
        this.updateExplosions(deltaTime);

        // Update players (movement, animations)
        this.updatePlayers(deltaTime);

        // Update pickups (animations)
        this.updatePickups(deltaTime);

        // Check game end conditions
        this.checkGameEndConditions();
    },

    /**
     * Render frame at variable timestep
     */
    renderFrame(deltaTime) {
        // Update UI elements that need smooth animation
        this.updateUIAnimations(deltaTime);

        // Trigger board re-render if needed
        const boardComponent = globalState.get('boardComponent');
        if (boardComponent && this.needsRerender) {
            boardComponent.setState('frameUpdate', Date.now());
            this.needsRerender = false;
        }
    },

    /**
     * Update performance counters and FPS display
     */
    updatePerformanceCounters(currentTime) {
        this.gameLoop.frameCount++;

        // Update FPS counter every second
        if (currentTime - this.gameLoop.lastFPSUpdate >= 1000) {
            this.gameLoop.fpsCounter = this.gameLoop.frameCount;
            this.gameLoop.frameCount = 0;
            this.gameLoop.lastFPSUpdate = currentTime;

            // Update FPS in global state for UI display
            globalState.set('game.fps', this.gameLoop.fpsCounter);
        }
    },

    /**
     * Update bomb timers and handle explosions
     */
    updateBombs(deltaTime) {
        const boardComponent = globalState.get('boardComponent');
        if (!boardComponent || !boardComponent.boardState) return;

        let bombsUpdated = false;

        for (let row = 0; row < 13; row++) {
            for (let col = 0; col < 15; col++) {
                const tile = boardComponent.boardState[row][col];

                if (tile.hasBomb && tile.bombData) {
                    const newTimeLeft = tile.bombData.timeLeft - (deltaTime / 1000);

                    if (newTimeLeft <= 0) {
                        // Bomb explodes
                        this.explodeBomb(row, col, tile.bombData);
                        bombsUpdated = true;
                    } else {
                        // Update bomb timer
                        tile.bombData.timeLeft = newTimeLeft;
                        bombsUpdated = true;
                    }
                }
            }
        }

        if (bombsUpdated) {
            this.needsRerender = true;
        }
    },

    /**
     * Update explosion durations
     */
    updateExplosions(deltaTime) {
        const boardComponent = globalState.get('boardComponent');
        if (!boardComponent || !boardComponent.boardState) return;

        let explosionsUpdated = false;

        for (let row = 0; row < 13; row++) {
            for (let col = 0; col < 15; col++) {
                const tile = boardComponent.boardState[row][col];

                if (tile.hasExplosion && tile.explosionData) {
                    const newDuration = (tile.explosionData.duration || 1000) - deltaTime;

                    if (newDuration <= 0) {
                        // Remove explosion
                        boardComponent.updateTileAt(row, col, {
                            hasExplosion: false,
                            explosionData: null
                        });
                        explosionsUpdated = true;
                    } else {
                        tile.explosionData.duration = newDuration;
                    }
                }
            }
        }

        if (explosionsUpdated) {
            this.needsRerender = true;
        }
    },

    /**
     * Update player animations and movement
     */
    updatePlayers(deltaTime) {
        // Player movement and animation updates will be implemented here
        // For now, just update animation states
        const boardComponent = globalState.get('boardComponent');
        if (!boardComponent || !boardComponent.boardState) return;

        // This will be expanded when player movement is implemented
    },

    /**
     * Update pickup animations
     */
    updatePickups(deltaTime) {
        // Pickup floating and glowing animations are handled by CSS
        // This method is reserved for future pickup logic
    },

    /**
     * Handle bomb explosion
     */
    explodeBomb(row, col, bombData) {
        const boardComponent = globalState.get('boardComponent');
        if (!boardComponent) return;

        const power = bombData.power || 1;

        // Remove the bomb
        boardComponent.updateTileAt(row, col, {
            hasBomb: false,
            bombData: null,
            hasExplosion: true,
            explosionData: { type: 'center', direction: 'none', intensity: power, duration: 1000 }
        });

        // Create explosion in all directions
        const directions = [
            { dr: -1, dc: 0, type: 'vertical', dir: 'up' },    // Up
            { dr: 1, dc: 0, type: 'vertical', dir: 'down' },   // Down
            { dr: 0, dc: -1, type: 'horizontal', dir: 'left' }, // Left
            { dr: 0, dc: 1, type: 'horizontal', dir: 'right' }  // Right
        ];

        directions.forEach(({ dr, dc, type, dir }) => {
            for (let i = 1; i <= power; i++) {
                const newRow = row + (dr * i);
                const newCol = col + (dc * i);

                if (!boardComponent.isValidPosition(newRow, newCol)) break;

                const tile = boardComponent.getTileAt(newRow, newCol);
                if (!tile) break;

                // Stop at indestructible walls
                if (tile.type === 'wall' && !tile.isDestructible) break;

                // Destroy destructible walls
                if (tile.type === 'wall' && tile.isDestructible) {
                    boardComponent.updateTileAt(newRow, newCol, {
                        type: 'ground',
                        isDestructible: false,
                        hasExplosion: true,
                        explosionData: { type: i === power ? 'end' : type, direction: dir, intensity: 1, duration: 1000 }
                    });
                    break; // Stop explosion after destroying wall
                }

                // Add explosion to ground tiles
                boardComponent.updateTileAt(newRow, newCol, {
                    hasExplosion: true,
                    explosionData: { type: i === power ? 'end' : type, direction: dir, intensity: 1, duration: 1000 }
                });

                // Chain reaction with other bombs
                if (tile.hasBomb) {
                    setTimeout(() => this.explodeBomb(newRow, newCol, tile.bombData), 100);
                }
            }
        });

        this.needsRerender = true;
        console.log(`Bomb exploded at (${row}, ${col}) with power ${power}`);
    },

    /**
     * Check for game end conditions
     */
    checkGameEndConditions() {
        const timeRemaining = globalState.get('game.timeRemaining') || 300;

        if (timeRemaining <= 0) {
            globalState.set('game.status', 'gameOver');
            this.stopGameLoop();
            console.log('Game over - time expired');
        }
    },

    /**
     * Update UI animations
     */
    updateUIAnimations(deltaTime) {
        // Update any UI elements that need smooth animation
        // This could include score counters, timer displays, etc.
    },

    /**
     * Mount the board component to its container
     */
    mountBoardComponent() {
        console.log('Attempting to mount board component...');
        const boardContainer = document.getElementById('board-container');
        console.log('Board container found:', !!boardContainer);

        if (boardContainer) {
            console.log('Creating new Board component...');
            this.boardComponent = new Board();
            console.log('Mounting board component to container...');
            this.boardComponent.mount(boardContainer);
            console.log('Board component mounted successfully');

            // Initialize rerender flag
            this.needsRerender = false;
        } else {
            console.error('Board container not found - DOM might not be ready yet');
            // Try again after a short delay
            setTimeout(() => {
                console.log('Retrying board component mount...');
                this.mountBoardComponent();
            }, 100);
        }
    }
});

/**
 * Game class for managing the application lifecycle
 */
export class Game {
    constructor() {
        this.app = null;
        this.gameComponent = null;
    }

    /**
     * Initialize and start the game application
     */
    init(mountElement) {
        if (!mountElement) {
            throw new Error('Mount element is required to initialize the game');
        }

        try {
            // Create the mini-framework app
            this.app = createApp();

            // Register components
            this.app.component('GameApp', GameApp);
            this.app.component('Board', Board);

            // Create and mount the main game component
            this.gameComponent = new GameApp();
            this.gameComponent.mount(mountElement);

            console.log('Bomberman game successfully initialized and mounted');
            
            return this;
        } catch (error) {
            console.error('Failed to initialize game:', error);
            throw error;
        }
    }

    /**
     * Get the current game instance
     */
    getGameComponent() {
        return this.gameComponent;
    }

    /**
     * Destroy the game and clean up resources
     */
    destroy() {
        if (this.gameComponent) {
            // Future: Add cleanup logic
            this.gameComponent = null;
        }
        this.app = null;
        console.log('Game destroyed');
    }
}

// Export the Game class as default
export default Game;
