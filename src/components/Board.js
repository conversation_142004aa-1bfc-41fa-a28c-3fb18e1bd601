/**
 * Board Component
 * 
 * Manages the Bomberman game board structure and rendering.
 * Creates a 13x15 grid with wall placement logic and maintains game state.
 * 
 * Board Layout:
 * - 13 rows × 15 columns
 * - Indestructible walls placed where both row and col are even
 * - All other tiles are ground tiles
 */

import { defineComponent, h, globalState, subscribeToState } from '../../mini-framework/src/framework.js';

export const Board = defineComponent({
    /**
     * Board configuration constants
     */
    BOARD_ROWS: 13,
    BOARD_COLS: 15,

    /**
     * Component state
     */
    state: {
        initialized: false
    },

    /**
     * Initialize board state and structure
     */
    afterMount() {
        console.log('Board component mounted');
        // Initialize the board state in memory
        this.initializeBoardState();

        // Subscribe to global state changes for future game mechanics
        subscribeToState('board', () => {
            this.setState('updated', Date.now());
        });

        // Store board reference globally for game logic access
        globalState.set('boardComponent', this);

        // Force initial render after state is initialized
        this.setState('initialized', true);
        console.log('Board component initialization complete');
    },

    /**
     * Initialize the 2D board array with tile data
     */
    initializeBoardState() {
        console.log('Initializing board state...');
        console.log('BOARD_ROWS:', this.BOARD_ROWS, 'BOARD_COLS:', this.BOARD_COLS);

        // Use constants directly since they might not be accessible as this.BOARD_ROWS
        const ROWS = 13;
        const COLS = 15;

        const boardState = [];

        for (let row = 0; row < ROWS; row++) {
            const boardRow = [];

            for (let col = 0; col < COLS; col++) {
                // Wall placement logic: walls where both row and col are even
                const isWall = (row % 2 === 0) && (col % 2 === 0);

                const tileData = {
                    type: isWall ? 'wall' : 'ground',
                    row: row,
                    col: col,
                    x: col,
                    y: row,
                    // Game state properties
                    hasPlayer: false,
                    hasBomb: false,
                    hasExplosion: false,
                    hasPickup: false,
                    // For destructible walls (future feature)
                    isDestructible: false
                };

                boardRow.push(tileData);
            }

            boardState.push(boardRow);
        }

        // Store board state in component and global state
        this.boardState = boardState;
        globalState.set('board.state', boardState);
        globalState.set('board.rows', ROWS);
        globalState.set('board.cols', COLS);

        console.log('Board state initialized:', {
            rows: ROWS,
            cols: COLS,
            boardStateLength: boardState.length,
            totalTiles: boardState.length > 0 ? boardState.length * boardState[0].length : 0
        });
    },

    /**
     * Get tile data at specific coordinates
     */
    getTileAt(row, col) {
        if (this.isValidPosition(row, col)) {
            return this.boardState[row][col];
        }
        return null;
    },

    /**
     * Update tile data at specific coordinates
     */
    updateTileAt(row, col, updates) {
        if (this.isValidPosition(row, col)) {
            this.boardState[row][col] = { ...this.boardState[row][col], ...updates };
            globalState.set('board.state', this.boardState);
            this.setState('updated', Date.now());
            return true;
        }
        return false;
    },

    /**
     * Check if position is within board bounds
     */
    isValidPosition(row, col) {
        return row >= 0 && row < this.BOARD_ROWS && 
               col >= 0 && col < this.BOARD_COLS;
    },

    /**
     * Get all tiles of a specific type
     */
    getTilesByType(type) {
        const tiles = [];
        for (let row = 0; row < this.BOARD_ROWS; row++) {
            for (let col = 0; col < this.BOARD_COLS; col++) {
                if (this.boardState[row][col].type === type) {
                    tiles.push({
                        ...this.boardState[row][col],
                        row,
                        col
                    });
                }
            }
        }
        return tiles;
    },

    /**
     * Get neighboring tiles (for explosion logic, pathfinding, etc.)
     */
    getNeighbors(row, col, includeDiagonals = false) {
        const neighbors = [];
        const directions = includeDiagonals 
            ? [[-1,-1], [-1,0], [-1,1], [0,-1], [0,1], [1,-1], [1,0], [1,1]]
            : [[-1,0], [0,-1], [0,1], [1,0]]; // Up, Left, Right, Down
        
        for (const [dRow, dCol] of directions) {
            const newRow = row + dRow;
            const newCol = col + dCol;
            
            if (this.isValidPosition(newRow, newCol)) {
                neighbors.push({
                    ...this.boardState[newRow][newCol],
                    row: newRow,
                    col: newCol
                });
            }
        }
        
        return neighbors;
    },

    /**
     * Find all walkable tiles (for AI pathfinding, spawn points, etc.)
     */
    getWalkableTiles() {
        return this.getTilesByType('ground').filter(tile => 
            !tile.hasPlayer && !tile.hasBomb
        );
    },

    /**
     * Reset board to initial state (for game restart)
     */
    resetBoard() {
        this.initializeBoardState();
    },

    /**
     * Render the game board
     */
    render() {
        console.log('Board render called, boardState exists:', !!this.boardState);

        // Always ensure board state is initialized
        if (!this.boardState) {
            console.log('Initializing board state in render...');
            this.initializeBoardState();
        }

        // Double check after initialization
        if (!this.boardState || this.boardState.length === 0) {
            console.log('Board state still not ready, showing loading...');
            return h('div', { class: 'board loading' }, [
                h('div', { class: 'loading-message' }, 'Loading board...')
            ]);
        }

        console.log('Rendering board with', this.boardState.length, 'rows and', this.boardState[0].length, 'cols');

        // Create board container with grid layout
        const ROWS = 13;
        const COLS = 15;

        const boardAttrs = {
            class: 'board',
            role: 'grid',
            'aria-label': `Bomberman game board, ${ROWS} rows by ${COLS} columns`,
            style: `
                display: grid;
                grid-template-rows: repeat(${ROWS}, 1fr);
                grid-template-columns: repeat(${COLS}, 1fr);
                gap: 1px;
            `
        };

        // Generate all tile elements directly using h() with string tags
        const tiles = [];
        for (let row = 0; row < ROWS; row++) {
            for (let col = 0; col < COLS; col++) {
                const tileData = this.boardState[row][col];

                // Create tile element directly
                const cssClasses = [
                    'tile',
                    `tile-${tileData.type}`,
                    `tile-row-${row}`,
                    `tile-col-${col}`
                ];

                // Add additional classes for future game states
                if (tileData.hasPlayer) cssClasses.push('tile-has-player');
                if (tileData.hasBomb) cssClasses.push('tile-has-bomb');
                if (tileData.hasExplosion) cssClasses.push('tile-has-explosion');
                if (tileData.hasPickup) cssClasses.push('tile-has-pickup');

                const tileAttrs = {
                    class: cssClasses.join(' '),
                    'data-row': row,
                    'data-col': col,
                    'data-type': tileData.type,
                    onclick: () => this.handleTileClick(row, col, tileData),
                    role: 'gridcell',
                    'aria-label': `${tileData.type} tile at row ${row}, column ${col}`
                };

                // Create tile content based on type
                const tileContent = tileData.type === 'wall'
                    ? h('div', { class: 'tile-content wall-content' }, [])
                    : h('div', { class: 'tile-content ground-content' }, []);

                tiles.push(h('div', tileAttrs, [tileContent]));
            }
        }

        return h('div', boardAttrs, tiles);
    },

    /**
     * Handle tile click events
     */
    handleTileClick(row, col, tileData) {
        console.log('Tile clicked:', { row, col, type: tileData.type });

        // Emit custom event for game logic to handle
        if (window.eventEmitter) {
            window.eventEmitter.emit('tile-clicked', {
                row: row,
                col: col,
                type: tileData.type,
                tileData: tileData
            });
        }
    }
});

export default Board;
