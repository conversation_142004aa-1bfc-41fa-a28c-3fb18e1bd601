/**
 * Board Component
 *
 * Manages the Bomberman game board structure and rendering.
 * Creates a 13x15 grid with wall placement logic and maintains game state.
 *
 * Board Layout:
 * - 13 rows × 15 columns
 * - Indestructible walls placed where both row and col are even
 * - Destructible walls scattered for strategic gameplay
 * - Player spawn areas at corners
 *
 * Rendering System:
 * - Pure DOM elements with CSS styling
 * - Layered rendering: Ground/Walls → Pickups → Bombs → Players → Explosions
 * - Animated elements with CSS animations
 * - Support for multiple players, bombs, explosions, and power-ups
 *
 * Game Elements Supported:
 * - Players (4 different colors, directional facing, movement animation)
 * - Bombs (with timer, fuse animation, critical state)
 * - Explosions (center, horizontal, vertical, directional)
 * - Pickups (bomb-up, fire-up, speed-up, kick, etc.)
 */

import { defineComponent, h, globalState, subscribeToState } from '../../mini-framework/src/framework.js';

export const Board = defineComponent({
    /**
     * Board configuration constants
     */
    BOARD_ROWS: 13,
    BOARD_COLS: 15,

    /**
     * Component state
     */
    state: {
        initialized: false
    },

    /**
     * Initialize board state and structure
     */
    afterMount() {
        console.log('Board component mounted');
        // Initialize the board state in memory
        this.initializeBoardState();

        // Subscribe to global state changes for future game mechanics
        subscribeToState('board', () => {
            this.setState('updated', Date.now());
        });

        // Store board reference globally for game logic access
        globalState.set('boardComponent', this);

        // Force initial render after state is initialized
        this.setState('initialized', true);
        console.log('Board component initialization complete');
    },

    /**
     * Initialize the 2D board array with tile data
     */
    initializeBoardState() {
        console.log('Initializing board state...');
        console.log('BOARD_ROWS:', this.BOARD_ROWS, 'BOARD_COLS:', this.BOARD_COLS);

        // Use constants directly since they might not be accessible as this.BOARD_ROWS
        const ROWS = 13;
        const COLS = 15;

        const boardState = [];

        for (let row = 0; row < ROWS; row++) {
            const boardRow = [];

            for (let col = 0; col < COLS; col++) {
                // Bomberman maze wall placement logic
                let tileType = 'ground';
                let isDestructible = false;

                // Border walls (indestructible)
                if (row === 0 || row === ROWS - 1 || col === 0 || col === COLS - 1) {
                    tileType = 'wall';
                    isDestructible = false;
                }
                // Internal structural walls (indestructible) - create maze structure
                else if (row % 2 === 0 && col % 2 === 0) {
                    tileType = 'wall';
                    isDestructible = false;
                }
                // Destructible walls - scattered throughout for strategic gameplay
                else if (this.shouldPlaceDestructibleWall(row, col, ROWS, COLS)) {
                    tileType = 'wall';
                    isDestructible = true;
                }

                const tileData = {
                    type: tileType,
                    row: row,
                    col: col,
                    x: col,
                    y: row,
                    // Game state properties
                    hasPlayer: false,
                    hasBomb: false,
                    hasExplosion: false,
                    hasPickup: false,
                    // Wall properties
                    isDestructible: isDestructible
                };

                boardRow.push(tileData);
            }

            boardState.push(boardRow);
        }

        // Store board state in component and global state
        this.boardState = boardState;
        globalState.set('board.state', boardState);
        globalState.set('board.rows', ROWS);
        globalState.set('board.cols', COLS);

        console.log('Board state initialized:', {
            rows: ROWS,
            cols: COLS,
            boardStateLength: boardState.length,
            totalTiles: boardState.length > 0 ? boardState.length * boardState[0].length : 0
        });
    },

    /**
     * Determine if a destructible wall should be placed at this position
     * Creates a balanced maze with strategic openings for gameplay
     */
    shouldPlaceDestructibleWall(row, col, totalRows, totalCols) {
        // Don't place destructible walls in player starting areas
        // Player 1 starts at (1,1), Player 2 at (1, totalCols-2), etc.
        const playerStartAreas = [
            {row: 1, col: 1}, {row: 1, col: 2}, {row: 2, col: 1}, // Player 1 area
            {row: 1, col: totalCols-2}, {row: 1, col: totalCols-3}, {row: 2, col: totalCols-2}, // Player 2 area
            {row: totalRows-2, col: 1}, {row: totalRows-2, col: 2}, {row: totalRows-3, col: 1}, // Player 3 area
            {row: totalRows-2, col: totalCols-2}, {row: totalRows-2, col: totalCols-3}, {row: totalRows-3, col: totalCols-2} // Player 4 area
        ];

        // Check if current position is in any player starting area
        for (const area of playerStartAreas) {
            if (row === area.row && col === area.col) {
                return false;
            }
        }

        // Create a pseudo-random but deterministic pattern for destructible walls
        // This ensures the same maze layout every time but with good gameplay balance
        const seed = (row * 31 + col * 17) % 100;

        // Place destructible walls with about 60% probability in valid areas
        // But ensure some corridors remain open for movement
        if (seed < 60) {
            // Don't place walls that would completely block corridors
            // Check if placing a wall here would create isolated areas

            // Ensure horizontal corridors (odd rows) have some openings
            if (row % 2 === 1) {
                // In horizontal corridors, place walls less frequently
                return seed < 40;
            }

            // Ensure vertical corridors (odd cols) have some openings
            if (col % 2 === 1) {
                // In vertical corridors, place walls less frequently
                return seed < 40;
            }

            // In intersection areas (odd row AND odd col), place walls more strategically
            return seed < 50;
        }

        return false;
    },

    /**
     * Get tile data at specific coordinates
     */
    getTileAt(row, col) {
        if (this.isValidPosition(row, col)) {
            return this.boardState[row][col];
        }
        return null;
    },

    /**
     * Update tile data at specific coordinates
     */
    updateTileAt(row, col, updates) {
        if (this.isValidPosition(row, col)) {
            this.boardState[row][col] = { ...this.boardState[row][col], ...updates };
            globalState.set('board.state', this.boardState);
            this.setState('updated', Date.now());
            return true;
        }
        return false;
    },

    /**
     * Check if position is within board bounds
     */
    isValidPosition(row, col) {
        return row >= 0 && row < this.BOARD_ROWS && 
               col >= 0 && col < this.BOARD_COLS;
    },

    /**
     * Get all tiles of a specific type
     */
    getTilesByType(type) {
        const tiles = [];
        for (let row = 0; row < this.BOARD_ROWS; row++) {
            for (let col = 0; col < this.BOARD_COLS; col++) {
                if (this.boardState[row][col].type === type) {
                    tiles.push({
                        ...this.boardState[row][col],
                        row,
                        col
                    });
                }
            }
        }
        return tiles;
    },

    /**
     * Get neighboring tiles (for explosion logic, pathfinding, etc.)
     */
    getNeighbors(row, col, includeDiagonals = false) {
        const neighbors = [];
        const directions = includeDiagonals 
            ? [[-1,-1], [-1,0], [-1,1], [0,-1], [0,1], [1,-1], [1,0], [1,1]]
            : [[-1,0], [0,-1], [0,1], [1,0]]; // Up, Left, Right, Down
        
        for (const [dRow, dCol] of directions) {
            const newRow = row + dRow;
            const newCol = col + dCol;
            
            if (this.isValidPosition(newRow, newCol)) {
                neighbors.push({
                    ...this.boardState[newRow][newCol],
                    row: newRow,
                    col: newCol
                });
            }
        }
        
        return neighbors;
    },

    /**
     * Find all walkable tiles (for AI pathfinding, spawn points, etc.)
     */
    getWalkableTiles() {
        return this.getTilesByType('ground').filter(tile => 
            !tile.hasPlayer && !tile.hasBomb
        );
    },

    /**
     * Reset board to initial state (for game restart)
     */
    resetBoard() {
        this.initializeBoardState();
    },

    /**
     * Render the game board
     */
    render() {
        console.log('Board render called, boardState exists:', !!this.boardState);

        // Always ensure board state is initialized
        if (!this.boardState) {
            console.log('Initializing board state in render...');
            this.initializeBoardState();
        }

        // Double check after initialization
        if (!this.boardState || this.boardState.length === 0) {
            console.log('Board state still not ready, showing loading...');
            return h('div', { class: 'board loading' }, [
                h('div', { class: 'loading-message' }, 'Loading board...')
            ]);
        }

        console.log('Rendering board with', this.boardState.length, 'rows and', this.boardState[0].length, 'cols');

        // Create board container with grid layout
        const ROWS = 13;
        const COLS = 15;

        const boardAttrs = {
            class: 'board',
            role: 'grid',
            'aria-label': `Bomberman game board, ${ROWS} rows by ${COLS} columns`,
            style: `
                display: grid;
                grid-template-rows: repeat(${ROWS}, 1fr);
                grid-template-columns: repeat(${COLS}, 1fr);
                gap: 1px;
            `
        };

        // Generate all tile elements directly using h() with string tags
        const tiles = [];
        for (let row = 0; row < ROWS; row++) {
            for (let col = 0; col < COLS; col++) {
                const tileData = this.boardState[row][col];

                // Create tile element directly
                const cssClasses = [
                    'tile',
                    `tile-${tileData.type}`,
                    `tile-row-${row}`,
                    `tile-col-${col}`
                ];

                // Add destructible wall class
                if (tileData.type === 'wall' && tileData.isDestructible) {
                    cssClasses.push('destructible');
                }

                // Add additional classes for future game states
                if (tileData.hasPlayer) cssClasses.push('tile-has-player');
                if (tileData.hasBomb) cssClasses.push('tile-has-bomb');
                if (tileData.hasExplosion) cssClasses.push('tile-has-explosion');
                if (tileData.hasPickup) cssClasses.push('tile-has-pickup');

                const tileAttrs = {
                    class: cssClasses.join(' '),
                    'data-row': row,
                    'data-col': col,
                    'data-type': tileData.type,
                    onclick: () => this.handleTileClick(row, col, tileData),
                    role: 'gridcell',
                    'aria-label': `${tileData.type} tile at row ${row}, column ${col}`
                };

                // Create tile content based on type
                const tileContent = this.renderTileContent(tileData);

                tiles.push(h('div', tileAttrs, [tileContent]));
            }
        }

        return h('div', boardAttrs, tiles);
    },

    /**
     * Render tile content including players, bombs, explosions, and pickups
     */
    renderTileContent(tileData) {
        const contentElements = [];

        // Base tile content (wall or ground)
        if (tileData.type === 'wall') {
            contentElements.push(
                h('div', { class: 'tile-content wall-content' }, [])
            );
        } else {
            contentElements.push(
                h('div', { class: 'tile-content ground-content' }, [])
            );
        }

        // Render explosion (highest priority - covers everything)
        if (tileData.hasExplosion) {
            contentElements.push(this.renderExplosion(tileData.explosionData));
        }

        // Render pickups (before players and bombs)
        if (tileData.hasPickup) {
            contentElements.push(this.renderPickup(tileData.pickupData));
        }

        // Render bomb (before players)
        if (tileData.hasBomb) {
            contentElements.push(this.renderBomb(tileData.bombData));
        }

        // Render player (top layer)
        if (tileData.hasPlayer) {
            contentElements.push(this.renderPlayer(tileData.playerData));
        }

        return h('div', { class: 'tile-container' }, contentElements);
    },

    /**
     * Render a player on the tile
     */
    renderPlayer(playerData) {
        const playerId = playerData?.id || 1;
        const direction = playerData?.direction || 'down';
        const isMoving = playerData?.isMoving || false;
        const isDead = playerData?.isDead || false;

        const playerClasses = [
            'game-element',
            'player',
            `player-${playerId}`,
            `facing-${direction}`,
            isMoving ? 'moving' : 'idle',
            isDead ? 'dead' : 'alive'
        ];

        return h('div', {
            class: playerClasses.join(' '),
            'data-player-id': playerId
        }, [
            h('div', { class: 'player-body' }, []),
            h('div', { class: 'player-face' }, []),
            h('div', { class: 'player-shadow' }, [])
        ]);
    },

    /**
     * Render a bomb on the tile
     */
    renderBomb(bombData) {
        const timeLeft = bombData?.timeLeft || 3;
        const power = bombData?.power || 1;
        const playerId = bombData?.playerId || 1;
        const isAboutToExplode = timeLeft <= 1;

        const bombClasses = [
            'game-element',
            'bomb',
            `bomb-power-${power}`,
            `bomb-player-${playerId}`,
            isAboutToExplode ? 'critical' : 'normal'
        ];

        return h('div', {
            class: bombClasses.join(' '),
            'data-time-left': timeLeft,
            'data-power': power
        }, [
            h('div', { class: 'bomb-body' }, []),
            h('div', { class: 'bomb-fuse' }, []),
            h('div', { class: 'bomb-timer' }, timeLeft.toString()),
            h('div', { class: 'bomb-shadow' }, [])
        ]);
    },

    /**
     * Render an explosion on the tile
     */
    renderExplosion(explosionData) {
        const type = explosionData?.type || 'center'; // center, horizontal, vertical, end
        const direction = explosionData?.direction || 'none'; // up, down, left, right, none
        const intensity = explosionData?.intensity || 1;

        const explosionClasses = [
            'game-element',
            'explosion',
            `explosion-${type}`,
            direction !== 'none' ? `explosion-${direction}` : '',
            `explosion-intensity-${intensity}`
        ].filter(Boolean);

        return h('div', {
            class: explosionClasses.join(' '),
            'data-type': type,
            'data-direction': direction
        }, [
            h('div', { class: 'explosion-core' }, []),
            h('div', { class: 'explosion-flames' }, []),
            h('div', { class: 'explosion-sparks' }, [])
        ]);
    },

    /**
     * Render a pickup/power-up on the tile
     */
    renderPickup(pickupData) {
        const type = pickupData?.type || 'bomb-up'; // bomb-up, fire-up, speed-up, etc.
        const value = pickupData?.value || 1;

        const pickupClasses = [
            'game-element',
            'pickup',
            `pickup-${type}`,
            `pickup-value-${value}`
        ];

        return h('div', {
            class: pickupClasses.join(' '),
            'data-type': type,
            'data-value': value
        }, [
            h('div', { class: 'pickup-icon' }, []),
            h('div', { class: 'pickup-glow' }, []),
            h('div', { class: 'pickup-shadow' }, [])
        ]);
    },

    /**
     * Add test game elements for demonstration
     */
    addTestElements() {
        if (!this.boardState) return;

        // Add test players at spawn points
        this.updateTileAt(1, 1, {
            hasPlayer: true,
            playerData: { id: 1, direction: 'down', isMoving: false, isDead: false }
        });

        this.updateTileAt(1, 13, {
            hasPlayer: true,
            playerData: { id: 2, direction: 'left', isMoving: true, isDead: false }
        });

        // Add test bombs
        this.updateTileAt(3, 3, {
            hasBomb: true,
            bombData: { timeLeft: 3, power: 2, playerId: 1 }
        });

        this.updateTileAt(5, 7, {
            hasBomb: true,
            bombData: { timeLeft: 1, power: 1, playerId: 2 }
        });

        // Add test explosion
        this.updateTileAt(7, 5, {
            hasExplosion: true,
            explosionData: { type: 'center', direction: 'none', intensity: 2 }
        });

        this.updateTileAt(7, 6, {
            hasExplosion: true,
            explosionData: { type: 'horizontal', direction: 'right', intensity: 1 }
        });

        // Add test pickups
        this.updateTileAt(9, 3, {
            hasPickup: true,
            pickupData: { type: 'bomb-up', value: 1 }
        });

        this.updateTileAt(9, 9, {
            hasPickup: true,
            pickupData: { type: 'fire-up', value: 1 }
        });

        this.updateTileAt(11, 7, {
            hasPickup: true,
            pickupData: { type: 'speed-up', value: 1 }
        });

        console.log('Test game elements added to the board');
    },

    /**
     * Clear all game elements from the board
     */
    clearGameElements() {
        if (!this.boardState) return;

        for (let row = 0; row < 13; row++) {
            for (let col = 0; col < 15; col++) {
                this.updateTileAt(row, col, {
                    hasPlayer: false,
                    hasBomb: false,
                    hasExplosion: false,
                    hasPickup: false,
                    playerData: null,
                    bombData: null,
                    explosionData: null,
                    pickupData: null
                });
            }
        }

        console.log('All game elements cleared from the board');
    },

    /**
     * Handle tile click events
     */
    handleTileClick(row, col, tileData) {
        console.log('Tile clicked:', { row, col, type: tileData.type });

        // Emit custom event for game logic to handle
        if (window.eventEmitter) {
            window.eventEmitter.emit('tile-clicked', {
                row: row,
                col: col,
                type: tileData.type,
                tileData: tileData
            });
        }
    }
});

export default Board;
