/**
 * Tile Component
 * 
 * Represents a single tile in the Bomberman game board.
 * Each tile can be either a 'wall' or 'ground' type and is aware of its position.
 * 
 * Props:
 * - type: 'wall' | 'ground' - The type of tile
 * - row: number - The row position (0-12)
 * - col: number - The column position (0-14)
 * - x: number - The x coordinate for game logic
 * - y: number - The y coordinate for game logic
 */

import { defineComponent, h } from '../../mini-framework/src/framework.js';

export const Tile = defineComponent({
    /**
     * Initialize tile component with position and type data
     */
    afterMount() {
        // Store tile data for future game logic integration
        this.tileData = {
            type: this.props.type,
            row: this.props.row,
            col: this.props.col,
            x: this.props.x || this.props.col,
            y: this.props.y || this.props.row,
            // Future properties for game mechanics
            hasPlayer: false,
            hasBomb: false,
            hasExplosion: false,
            hasPickup: false
        };
    },

    /**
     * Get the current tile data (useful for game logic)
     */
    getTileData() {
        return { ...this.tileData };
    },

    /**
     * Update tile properties (for future game mechanics)
     */
    updateTile(updates) {
        this.tileData = { ...this.tileData, ...updates };
        // Trigger re-render if needed
        this.setState('updated', Date.now());
    },

    /**
     * Check if tile can be walked on (for future player movement)
     */
    isWalkable() {
        return this.tileData.type === 'ground' && 
               !this.tileData.hasBomb && 
               !this.tileData.hasPlayer;
    },

    /**
     * Check if tile can have a bomb placed on it
     */
    canPlaceBomb() {
        return this.tileData.type === 'ground' && 
               !this.tileData.hasBomb;
    },

    /**
     * Handle tile interactions (for future game mechanics)
     */
    handleTileClick() {
        // Emit custom event for game logic to handle
        if (window.eventEmitter) {
            window.eventEmitter.emit('tile-clicked', {
                row: this.tileData.row,
                col: this.tileData.col,
                type: this.tileData.type,
                tileData: this.getTileData()
            });
        }
    },

    /**
     * Render the tile element
     */
    render() {
        const { type, row, col } = this.props;
        
        // Generate CSS classes based on tile type and state
        const cssClasses = [
            'tile',
            `tile-${type}`,
            `tile-row-${row}`,
            `tile-col-${col}`
        ];

        // Add additional classes for future game states
        if (this.tileData) {
            if (this.tileData.hasPlayer) cssClasses.push('tile-has-player');
            if (this.tileData.hasBomb) cssClasses.push('tile-has-bomb');
            if (this.tileData.hasExplosion) cssClasses.push('tile-has-explosion');
            if (this.tileData.hasPickup) cssClasses.push('tile-has-pickup');
        }

        // Create tile attributes
        const tileAttrs = {
            class: cssClasses.join(' '),
            'data-row': row,
            'data-col': col,
            'data-type': type,
            onclick: () => this.handleTileClick(),
            // Add ARIA attributes for accessibility
            role: 'gridcell',
            'aria-label': `${type} tile at row ${row}, column ${col}`
        };

        // Render tile content based on type
        const tileContent = [];
        
        if (type === 'wall') {
            // Wall tiles are solid blocks
            tileContent.push(
                h('div', { class: 'tile-content wall-content' }, [])
            );
        } else {
            // Ground tiles can contain game elements
            tileContent.push(
                h('div', { class: 'tile-content ground-content' }, [
                    // Future: Player, bomb, explosion, pickup elements will go here
                ])
            );
        }

        return h('div', tileAttrs, tileContent);
    }
});

export default Tile;
