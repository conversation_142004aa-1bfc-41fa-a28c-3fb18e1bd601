/**
 * Game Loop Module
 * 
 * Handles the main game loop using requestAnimationFrame for smooth animations
 * and fixed timestep updates for consistent game logic.
 */

export class GameLoop {
    constructor() {
        this.config = {
            targetFPS: 60,
            gameTickRate: 1000 / 30, // 30 game ticks per second
            maxDeltaTime: 1000 / 15  // Cap delta time to prevent spiral of death
        };

        this.state = {
            isRunning: false,
            lastTime: 0,
            deltaTime: 0,
            accumulator: 0,
            animationId: null,
            
            // Performance tracking
            frameCount: 0,
            fpsCounter: 0,
            lastFPSUpdate: 0
        };

        // Callbacks
        this.updateCallback = null;
        this.renderCallback = null;
        this.performanceCallback = null;
    }

    /**
     * Set the update callback for game logic
     */
    setUpdateCallback(callback) {
        this.updateCallback = callback;
    }

    /**
     * Set the render callback for frame rendering
     */
    setRenderCallback(callback) {
        this.renderCallback = callback;
    }

    /**
     * Set the performance callback for FPS updates
     */
    setPerformanceCallback(callback) {
        this.performanceCallback = callback;
    }

    /**
     * Start the game loop
     */
    start() {
        if (this.state.isRunning) return;
        
        this.state.isRunning = true;
        this.state.lastTime = performance.now();
        this.state.lastFPSUpdate = this.state.lastTime;
        
        this.run();
        console.log('Game loop started');
    }

    /**
     * Stop the game loop
     */
    stop() {
        if (!this.state.isRunning) return;
        
        this.state.isRunning = false;
        if (this.state.animationId) {
            cancelAnimationFrame(this.state.animationId);
            this.state.animationId = null;
        }
        console.log('Game loop stopped');
    }

    /**
     * Main game loop using requestAnimationFrame
     */
    run() {
        if (!this.state.isRunning) return;

        const currentTime = performance.now();
        this.state.deltaTime = Math.min(
            currentTime - this.state.lastTime,
            this.config.maxDeltaTime
        );
        this.state.lastTime = currentTime;

        // Add to accumulator for fixed timestep updates
        this.state.accumulator += this.state.deltaTime;

        // Fixed timestep game logic updates
        while (this.state.accumulator >= this.config.gameTickRate) {
            if (this.updateCallback) {
                this.updateCallback(this.config.gameTickRate);
            }
            this.state.accumulator -= this.config.gameTickRate;
        }

        // Variable timestep rendering
        if (this.renderCallback) {
            this.renderCallback(this.state.deltaTime);
        }

        // Update performance counters
        this.updatePerformanceCounters(currentTime);

        // Schedule next frame
        this.state.animationId = requestAnimationFrame(() => this.run());
    }

    /**
     * Update performance counters and FPS display
     */
    updatePerformanceCounters(currentTime) {
        this.state.frameCount++;
        
        // Update FPS counter every second
        if (currentTime - this.state.lastFPSUpdate >= 1000) {
            this.state.fpsCounter = this.state.frameCount;
            this.state.frameCount = 0;
            this.state.lastFPSUpdate = currentTime;
            
            // Call performance callback if set
            if (this.performanceCallback) {
                this.performanceCallback(this.state.fpsCounter);
            }
        }
    }

    /**
     * Get current FPS
     */
    getFPS() {
        return this.state.fpsCounter;
    }

    /**
     * Get current delta time
     */
    getDeltaTime() {
        return this.state.deltaTime;
    }

    /**
     * Check if the loop is running
     */
    isRunning() {
        return this.state.isRunning;
    }

    /**
     * Get loop statistics
     */
    getStats() {
        return {
            fps: this.state.fpsCounter,
            deltaTime: this.state.deltaTime,
            isRunning: this.state.isRunning,
            frameCount: this.state.frameCount
        };
    }
}
