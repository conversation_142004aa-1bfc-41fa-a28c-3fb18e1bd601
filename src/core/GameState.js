/**
 * Game State Module
 * 
 * Manages the global game state including game status, score, timer,
 * and other game-wide properties.
 */

import { globalState } from '../../mini-framework/src/framework.js';

export class GameState {
    constructor() {
        this.defaultState = {
            status: 'initialized', // initialized, playing, paused, gameOver
            level: 1,
            score: 0,
            lives: 3,
            timeRemaining: 300, // 5 minutes in seconds
            players: [],
            enemies: [],
            bombs: [],
            explosions: [],
            pickups: [],
            settings: {
                soundEnabled: true,
                musicEnabled: true,
                difficulty: 'normal', // easy, normal, hard
                playerCount: 1 // 1-4 players
            }
        };
    }

    /**
     * Initialize game state with default values
     */
    initialize() {
        // Set game state
        globalState.set('game', {
            status: this.defaultState.status,
            level: this.defaultState.level,
            score: this.defaultState.score,
            lives: this.defaultState.lives,
            timeRemaining: this.defaultState.timeRemaining,
            players: [...this.defaultState.players],
            enemies: [...this.defaultState.enemies],
            bombs: [...this.defaultState.bombs],
            explosions: [...this.defaultState.explosions],
            pickups: [...this.defaultState.pickups]
        });

        // Set game settings
        globalState.set('settings', { ...this.defaultState.settings });

        console.log('Game state initialized');
    }

    /**
     * Reset game state to defaults
     */
    reset() {
        this.initialize();
        console.log('Game state reset');
    }

    /**
     * Get current game status
     */
    getStatus() {
        return globalState.get('game.status') || this.defaultState.status;
    }

    /**
     * Set game status
     */
    setStatus(status) {
        globalState.set('game.status', status);
    }

    /**
     * Get current score
     */
    getScore() {
        return globalState.get('game.score') || this.defaultState.score;
    }

    /**
     * Set score
     */
    setScore(score) {
        globalState.set('game.score', Math.max(0, score));
    }

    /**
     * Add to score
     */
    addScore(points) {
        const currentScore = this.getScore();
        this.setScore(currentScore + points);
    }

    /**
     * Get current level
     */
    getLevel() {
        return globalState.get('game.level') || this.defaultState.level;
    }

    /**
     * Set level
     */
    setLevel(level) {
        globalState.set('game.level', Math.max(1, level));
    }

    /**
     * Get remaining time
     */
    getTimeRemaining() {
        return globalState.get('game.timeRemaining') || this.defaultState.timeRemaining;
    }

    /**
     * Set remaining time
     */
    setTimeRemaining(time) {
        globalState.set('game.timeRemaining', Math.max(0, time));
    }

    /**
     * Update timer (called from game loop)
     */
    updateTimer(deltaTime) {
        if (this.getStatus() === 'playing') {
            const currentTime = this.getTimeRemaining();
            const newTime = Math.max(0, currentTime - (deltaTime / 1000));
            this.setTimeRemaining(newTime);
            
            // Check for time up
            if (newTime <= 0) {
                this.setStatus('gameOver');
                return true; // Game ended
            }
        }
        return false;
    }

    /**
     * Get current lives
     */
    getLives() {
        return globalState.get('game.lives') || this.defaultState.lives;
    }

    /**
     * Set lives
     */
    setLives(lives) {
        globalState.set('game.lives', Math.max(0, lives));
    }

    /**
     * Lose a life
     */
    loseLife() {
        const currentLives = this.getLives();
        this.setLives(currentLives - 1);
        
        // Check for game over
        if (currentLives <= 1) {
            this.setStatus('gameOver');
            return true; // Game ended
        }
        return false;
    }

    /**
     * Get players array
     */
    getPlayers() {
        return globalState.get('game.players') || [];
    }

    /**
     * Set players array
     */
    setPlayers(players) {
        globalState.set('game.players', [...players]);
    }

    /**
     * Add a player
     */
    addPlayer(player) {
        const players = this.getPlayers();
        players.push(player);
        this.setPlayers(players);
    }

    /**
     * Remove a player
     */
    removePlayer(playerId) {
        const players = this.getPlayers();
        const filteredPlayers = players.filter(p => p.id !== playerId);
        this.setPlayers(filteredPlayers);
    }

    /**
     * Get bombs array
     */
    getBombs() {
        return globalState.get('game.bombs') || [];
    }

    /**
     * Set bombs array
     */
    setBombs(bombs) {
        globalState.set('game.bombs', [...bombs]);
    }

    /**
     * Add a bomb
     */
    addBomb(bomb) {
        const bombs = this.getBombs();
        bombs.push(bomb);
        this.setBombs(bombs);
    }

    /**
     * Remove a bomb
     */
    removeBomb(bombId) {
        const bombs = this.getBombs();
        const filteredBombs = bombs.filter(b => b.id !== bombId);
        this.setBombs(filteredBombs);
    }

    /**
     * Get explosions array
     */
    getExplosions() {
        return globalState.get('game.explosions') || [];
    }

    /**
     * Set explosions array
     */
    setExplosions(explosions) {
        globalState.set('game.explosions', [...explosions]);
    }

    /**
     * Add an explosion
     */
    addExplosion(explosion) {
        const explosions = this.getExplosions();
        explosions.push(explosion);
        this.setExplosions(explosions);
    }

    /**
     * Remove an explosion
     */
    removeExplosion(explosionId) {
        const explosions = this.getExplosions();
        const filteredExplosions = explosions.filter(e => e.id !== explosionId);
        this.setExplosions(filteredExplosions);
    }

    /**
     * Get pickups array
     */
    getPickups() {
        return globalState.get('game.pickups') || [];
    }

    /**
     * Set pickups array
     */
    setPickups(pickups) {
        globalState.set('game.pickups', [...pickups]);
    }

    /**
     * Add a pickup
     */
    addPickup(pickup) {
        const pickups = this.getPickups();
        pickups.push(pickup);
        this.setPickups(pickups);
    }

    /**
     * Remove a pickup
     */
    removePickup(pickupId) {
        const pickups = this.getPickups();
        const filteredPickups = pickups.filter(p => p.id !== pickupId);
        this.setPickups(filteredPickups);
    }

    /**
     * Get game settings
     */
    getSettings() {
        return globalState.get('settings') || this.defaultState.settings;
    }

    /**
     * Update a setting
     */
    setSetting(key, value) {
        const settings = this.getSettings();
        settings[key] = value;
        globalState.set('settings', settings);
    }

    /**
     * Get a specific setting
     */
    getSetting(key) {
        const settings = this.getSettings();
        return settings[key];
    }

    /**
     * Check if game is playing
     */
    isPlaying() {
        return this.getStatus() === 'playing';
    }

    /**
     * Check if game is paused
     */
    isPaused() {
        return this.getStatus() === 'paused';
    }

    /**
     * Check if game is over
     */
    isGameOver() {
        return this.getStatus() === 'gameOver';
    }

    /**
     * Get complete game state for debugging
     */
    getCompleteState() {
        return {
            game: globalState.get('game'),
            settings: globalState.get('settings')
        };
    }
}
