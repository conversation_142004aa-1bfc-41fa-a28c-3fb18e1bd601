/**
 * Input Manager Module
 * 
 * Handles keyboard input, mouse events, and touch controls for the game.
 * Provides a clean interface for game controls and input mapping.
 */

export class InputManager {
    constructor() {
        this.keys = new Map();
        this.keyBindings = new Map();
        this.callbacks = new Map();
        
        // Default key bindings
        this.setupDefaultBindings();
        
        // Event listeners
        this.setupEventListeners();
        
        console.log('Input Manager initialized');
    }

    /**
     * Setup default key bindings
     */
    setupDefaultBindings() {
        // Player 1 controls
        this.keyBindings.set('ArrowUp', 'player1_up');
        this.keyBindings.set('ArrowDown', 'player1_down');
        this.keyBindings.set('ArrowLeft', 'player1_left');
        this.keyBindings.set('ArrowRight', 'player1_right');
        this.keyBindings.set('Space', 'player1_bomb');
        
        // Alternative Player 1 controls (WASD)
        this.keyBindings.set('KeyW', 'player1_up');
        this.keyBindings.set('KeyS', 'player1_down');
        this.keyBindings.set('KeyA', 'player1_left');
        this.keyBindings.set('KeyD', 'player1_right');
        this.keyBindings.set('KeyQ', 'player1_bomb');
        
        // Player 2 controls (IJKL)
        this.keyBindings.set('KeyI', 'player2_up');
        this.keyBindings.set('KeyK', 'player2_down');
        this.keyBindings.set('KeyJ', 'player2_left');
        this.keyBindings.set('KeyL', 'player2_right');
        this.keyBindings.set('KeyU', 'player2_bomb');
        
        // Game controls
        this.keyBindings.set('Escape', 'pause');
        this.keyBindings.set('Enter', 'start');
        this.keyBindings.set('KeyR', 'reset');
        this.keyBindings.set('KeyM', 'mute');
        this.keyBindings.set('KeyP', 'pause');
    }

    /**
     * Setup event listeners for keyboard and mouse
     */
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Mouse events
        document.addEventListener('click', (e) => this.handleClick(e));
        document.addEventListener('contextmenu', (e) => this.handleRightClick(e));
        
        // Window events
        window.addEventListener('blur', () => this.handleWindowBlur());
        window.addEventListener('focus', () => this.handleWindowFocus());
        
        // Touch events for mobile support
        document.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e));
    }

    /**
     * Handle key down events
     */
    handleKeyDown(event) {
        const keyCode = event.code;
        const action = this.keyBindings.get(keyCode);
        
        // Prevent default for game keys
        if (action) {
            event.preventDefault();
        }
        
        // Track key state
        this.keys.set(keyCode, true);
        
        // Call action callback if mapped
        if (action && !this.isKeyRepeating(keyCode)) {
            this.triggerAction(action, { type: 'keydown', keyCode, event });
        }
    }

    /**
     * Handle key up events
     */
    handleKeyUp(event) {
        const keyCode = event.code;
        const action = this.keyBindings.get(keyCode);
        
        // Track key state
        this.keys.set(keyCode, false);
        
        // Call action callback if mapped
        if (action) {
            this.triggerAction(action + '_release', { type: 'keyup', keyCode, event });
        }
    }

    /**
     * Handle mouse click events
     */
    handleClick(event) {
        this.triggerAction('click', { 
            type: 'click', 
            x: event.clientX, 
            y: event.clientY, 
            target: event.target,
            event 
        });
    }

    /**
     * Handle right click events
     */
    handleRightClick(event) {
        event.preventDefault(); // Prevent context menu
        this.triggerAction('rightclick', { 
            type: 'rightclick', 
            x: event.clientX, 
            y: event.clientY, 
            target: event.target,
            event 
        });
    }

    /**
     * Handle window blur (game loses focus)
     */
    handleWindowBlur() {
        // Clear all key states when window loses focus
        this.keys.clear();
        this.triggerAction('window_blur', { type: 'blur' });
    }

    /**
     * Handle window focus (game gains focus)
     */
    handleWindowFocus() {
        this.triggerAction('window_focus', { type: 'focus' });
    }

    /**
     * Handle touch start events
     */
    handleTouchStart(event) {
        const touch = event.touches[0];
        this.triggerAction('touch_start', {
            type: 'touchstart',
            x: touch.clientX,
            y: touch.clientY,
            target: event.target,
            event
        });
    }

    /**
     * Handle touch end events
     */
    handleTouchEnd(event) {
        this.triggerAction('touch_end', {
            type: 'touchend',
            event
        });
    }

    /**
     * Check if a key is currently pressed
     */
    isKeyPressed(keyCode) {
        return this.keys.get(keyCode) || false;
    }

    /**
     * Check if key is repeating (held down)
     */
    isKeyRepeating(keyCode) {
        // Simple repeat detection - could be enhanced
        return this.keys.get(keyCode) === true;
    }

    /**
     * Register a callback for an action
     */
    onAction(action, callback) {
        if (!this.callbacks.has(action)) {
            this.callbacks.set(action, []);
        }
        this.callbacks.get(action).push(callback);
    }

    /**
     * Remove a callback for an action
     */
    offAction(action, callback) {
        if (this.callbacks.has(action)) {
            const callbacks = this.callbacks.get(action);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * Trigger an action and call all registered callbacks
     */
    triggerAction(action, data = {}) {
        if (this.callbacks.has(action)) {
            const callbacks = this.callbacks.get(action);
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in input callback for action "${action}":`, error);
                }
            });
        }
    }

    /**
     * Set a custom key binding
     */
    setKeyBinding(keyCode, action) {
        this.keyBindings.set(keyCode, action);
    }

    /**
     * Remove a key binding
     */
    removeKeyBinding(keyCode) {
        this.keyBindings.delete(keyCode);
    }

    /**
     * Get current key bindings
     */
    getKeyBindings() {
        return new Map(this.keyBindings);
    }

    /**
     * Get currently pressed keys
     */
    getPressedKeys() {
        const pressed = [];
        for (const [key, isPressed] of this.keys) {
            if (isPressed) {
                pressed.push(key);
            }
        }
        return pressed;
    }

    /**
     * Clear all key states
     */
    clearKeys() {
        this.keys.clear();
    }

    /**
     * Destroy the input manager and remove event listeners
     */
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('click', this.handleClick);
        document.removeEventListener('contextmenu', this.handleRightClick);
        window.removeEventListener('blur', this.handleWindowBlur);
        window.removeEventListener('focus', this.handleWindowFocus);
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchend', this.handleTouchEnd);
        
        this.keys.clear();
        this.callbacks.clear();
        this.keyBindings.clear();
        
        console.log('Input Manager destroyed');
    }
}
