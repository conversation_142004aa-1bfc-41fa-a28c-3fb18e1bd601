/**
 * Bomberman Game Styles
 * 
 * CSS styles for the Bomberman game board and UI components.
 * Includes responsive design for different screen sizes.
 */

/* ===== RESET AND BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* ===== GAME CONTAINER ===== */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 1200px;
    width: 100%;
    margin: 20px;
}

/* ===== GAME HEADER ===== */
.game-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.game-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.btn-start {
    background: #27ae60;
    color: white;
}

.btn-start:hover:not(:disabled) {
    background: #229954;
    transform: translateY(-2px);
}

.btn-reset {
    background: #e74c3c;
    color: white;
}

.btn-reset:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-pause {
    background: #f39c12;
    color: white;
}

.btn-pause:hover:not(:disabled) {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ===== GAME INFO PANEL ===== */
.game-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
}

.info-label {
    font-weight: bold;
    color: #2c3e50;
}

.info-value {
    color: #e74c3c;
    font-weight: bold;
}

/* ===== GAME BOARD ===== */
.game-main {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.board-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.board {
    display: grid;
    grid-template-rows: repeat(13, 1fr);
    grid-template-columns: repeat(15, 1fr);
    gap: 1px;
    background: #34495e;
    border: 3px solid #2c3e50;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    /* Responsive sizing */
    width: min(90vw, 750px);
    height: min(78vw, 650px);
    max-width: 750px;
    max-height: 650px;
}

.board.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-message {
    font-size: 1.2rem;
    color: #7f8c8d;
}

/* ===== TILE STYLES ===== */
.tile {
    position: relative;
    border-radius: 2px;
    transition: all 0.2s ease;
    cursor: pointer;
    overflow: hidden;
}

.tile:hover {
    transform: scale(1.05);
    z-index: 10;
}

.tile-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* Wall tiles */
.tile-wall {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    border: 1px solid #6c7b7d;
}

/* Indestructible walls (structural/border walls) */
.wall-content {
    background: repeating-linear-gradient(
        45deg,
        #bdc3c7,
        #bdc3c7 2px,
        #95a5a6 2px,
        #95a5a6 4px
    );
    border-radius: 1px;
}

/* Destructible walls */
.tile-wall.destructible {
    background: linear-gradient(135deg, #d35400 0%, #e67e22 100%);
    border: 1px solid #a0522d;
}

.tile-wall.destructible .wall-content {
    background: repeating-linear-gradient(
        45deg,
        #f39c12,
        #f39c12 3px,
        #e67e22 3px,
        #e67e22 6px
    );
    border-radius: 2px;
    position: relative;
}

/* Add a subtle texture to destructible walls */
.tile-wall.destructible .wall-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 1px, transparent 1px),
                radial-gradient(circle at 70% 70%, rgba(0,0,0,0.2) 1px, transparent 1px);
    background-size: 8px 8px;
}

.tile-wall:hover {
    transform: none; /* Walls don't scale on hover */
    cursor: default;
}

/* Destructible walls can be hovered for interaction */
.tile-wall.destructible:hover {
    background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    cursor: pointer;
}

/* Ground tiles */
.tile-ground {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    position: relative;
}

.ground-content {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    position: relative;
}

/* Add a subtle grid pattern to ground tiles */
.ground-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(180deg, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 8px 8px;
}

.tile-ground:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
}

/* Player spawn areas - visual indicators */
.tile-row-1.tile-col-1,
.tile-row-1.tile-col-13,
.tile-row-11.tile-col-1,
.tile-row-11.tile-col-13 {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 2px solid #4caf50;
    position: relative;
}

.tile-row-1.tile-col-1::after,
.tile-row-1.tile-col-13::after,
.tile-row-11.tile-col-1::after,
.tile-row-11.tile-col-13::after {
    content: '👤';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    opacity: 0.6;
}

/* Future game element styles */
.tile-has-player {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: 2px solid #1976d2;
}

.tile-has-bomb {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    animation: bomb-pulse 1s infinite;
}

.tile-has-explosion {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    animation: explosion-flash 0.5s ease-out;
}

.tile-has-pickup {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    animation: pickup-glow 2s infinite;
}

/* ===== ANIMATIONS ===== */
@keyframes bomb-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes explosion-flash {
    0% { background: #f1c40f; transform: scale(1.2); }
    100% { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); transform: scale(1); }
}

@keyframes pickup-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(155, 89, 182, 0.5); }
    50% { box-shadow: 0 0 15px rgba(155, 89, 182, 0.8); }
}

/* ===== GAME FOOTER ===== */
.game-footer {
    text-align: center;
    padding-top: 15px;
    border-top: 2px solid #e0e0e0;
}

.instructions h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.instructions p {
    margin: 5px 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .game-controls {
        gap: 8px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .game-info {
        gap: 15px;
        font-size: 1rem;
    }
    
    .board {
        width: 95vw;
        height: 82vw;
        max-width: 600px;
        max-height: 520px;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .board {
        width: 98vw;
        height: 85vw;
        max-width: 450px;
        max-height: 390px;
    }
    
    .instructions p {
        font-size: 0.8rem;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    .tile,
    .btn {
        transition: none;
    }
    
    .tile:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
    
    @keyframes bomb-pulse,
    @keyframes explosion-flash,
    @keyframes pickup-glow {
        animation: none;
    }
}

/* Focus styles for keyboard navigation */
.tile:focus,
.btn:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}
