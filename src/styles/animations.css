/**
 * Animation Styles
 * 
 * All CSS animations for game elements, UI components, and effects.
 */

/* ===== PLAYER ANIMATIONS ===== */
@keyframes player-walk {
    0%, 100% { 
        transform: translateY(0px) scale(1); 
    }
    50% { 
        transform: translateY(-2px) scale(1.05); 
    }
}

@keyframes player-death {
    0% { 
        transform: scale(1) rotate(0deg); 
        opacity: 1; 
    }
    50% { 
        transform: scale(1.2) rotate(180deg); 
        opacity: 0.7; 
    }
    100% { 
        transform: scale(0.8) rotate(360deg); 
        opacity: 0.3; 
    }
}

/* ===== BOMB ANIMATIONS ===== */
@keyframes bomb-pulse {
    0%, 100% { 
        transform: scale(1); 
    }
    50% { 
        transform: scale(1.05); 
    }
}

@keyframes bomb-critical {
    0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 0 10px var(--color-secondary); 
    }
    50% { 
        transform: scale(1.1); 
        box-shadow: 0 0 20px var(--color-secondary-dark); 
    }
}

@keyframes fuse-burn {
    0% { 
        height: 20%; 
        background: linear-gradient(to top, var(--color-warning-dark), var(--color-warning)); 
    }
    50% { 
        height: 15%; 
        background: linear-gradient(to top, var(--color-secondary), var(--color-warning)); 
    }
    100% { 
        height: 5%; 
        background: linear-gradient(to top, var(--color-secondary-dark), var(--color-secondary)); 
    }
}

@keyframes fuse-critical {
    0%, 100% { 
        opacity: 1; 
    }
    50% { 
        opacity: 0.3; 
    }
}

/* ===== EXPLOSION ANIMATIONS ===== */
@keyframes explosion-appear {
    0% { 
        transform: scale(0); 
        opacity: 0; 
    }
    50% { 
        transform: scale(1.2); 
        opacity: 1; 
    }
    100% { 
        transform: scale(1); 
        opacity: 1; 
    }
}

@keyframes explosion-pulse {
    0%, 100% { 
        transform: scale(1); 
    }
    50% { 
        transform: scale(1.1); 
    }
}

@keyframes flames-flicker {
    0%, 100% { 
        opacity: 0.8; 
        transform: rotate(0deg); 
    }
    25% { 
        opacity: 1; 
        transform: rotate(2deg); 
    }
    50% { 
        opacity: 0.9; 
        transform: rotate(-1deg); 
    }
    75% { 
        opacity: 1; 
        transform: rotate(1deg); 
    }
}

@keyframes sparks-twinkle {
    0%, 100% { 
        opacity: 0.8; 
    }
    50% { 
        opacity: 1; 
    }
}

/* ===== PICKUP ANIMATIONS ===== */
@keyframes pickup-float {
    0%, 100% { 
        transform: translateY(0px); 
    }
    50% { 
        transform: translateY(-3px); 
    }
}

@keyframes pickup-glow-animation {
    0%, 100% { 
        opacity: 0.6; 
        transform: scale(1); 
    }
    50% { 
        opacity: 1; 
        transform: scale(1.1); 
    }
}

/* ===== UI ANIMATIONS ===== */
@keyframes fade-in {
    from { 
        opacity: 0; 
    }
    to { 
        opacity: 1; 
    }
}

@keyframes fade-out {
    from { 
        opacity: 1; 
    }
    to { 
        opacity: 0; 
    }
}

@keyframes slide-in-up {
    from { 
        transform: translateY(100%); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0); 
        opacity: 1; 
    }
}

@keyframes slide-in-down {
    from { 
        transform: translateY(-100%); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0); 
        opacity: 1; 
    }
}

@keyframes slide-in-left {
    from { 
        transform: translateX(-100%); 
        opacity: 0; 
    }
    to { 
        transform: translateX(0); 
        opacity: 1; 
    }
}

@keyframes slide-in-right {
    from { 
        transform: translateX(100%); 
        opacity: 0; 
    }
    to { 
        transform: translateX(0); 
        opacity: 1; 
    }
}

@keyframes bounce-in {
    0% { 
        transform: scale(0.3); 
        opacity: 0; 
    }
    50% { 
        transform: scale(1.05); 
        opacity: 0.8; 
    }
    70% { 
        transform: scale(0.9); 
        opacity: 1; 
    }
    100% { 
        transform: scale(1); 
        opacity: 1; 
    }
}

@keyframes shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    10%, 30%, 50%, 70%, 90% { 
        transform: translateX(-2px); 
    }
    20%, 40%, 60%, 80% { 
        transform: translateX(2px); 
    }
}

@keyframes pulse {
    0%, 100% { 
        transform: scale(1); 
        opacity: 1; 
    }
    50% { 
        transform: scale(1.05); 
        opacity: 0.8; 
    }
}

@keyframes rotate {
    from { 
        transform: rotate(0deg); 
    }
    to { 
        transform: rotate(360deg); 
    }
}

@keyframes wobble {
    0% { 
        transform: translateX(0%); 
    }
    15% { 
        transform: translateX(-25%) rotate(-5deg); 
    }
    30% { 
        transform: translateX(20%) rotate(3deg); 
    }
    45% { 
        transform: translateX(-15%) rotate(-3deg); 
    }
    60% { 
        transform: translateX(10%) rotate(2deg); 
    }
    75% { 
        transform: translateX(-5%) rotate(-1deg); 
    }
    100% { 
        transform: translateX(0%); 
    }
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes loading-dots {
    0%, 20% { 
        color: rgba(0, 0, 0, 0); 
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 
                     0.5em 0 0 rgba(0, 0, 0, 0); 
    }
    40% { 
        color: black; 
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 
                     0.5em 0 0 rgba(0, 0, 0, 0); 
    }
    60% { 
        text-shadow: 0.25em 0 0 black, 
                     0.5em 0 0 rgba(0, 0, 0, 0); 
    }
    80%, 100% { 
        text-shadow: 0.25em 0 0 black, 
                     0.5em 0 0 black; 
    }
}

@keyframes loading-spinner {
    0% { 
        transform: rotate(0deg); 
    }
    100% { 
        transform: rotate(360deg); 
    }
}

/* ===== UTILITY ANIMATION CLASSES ===== */
.animate-fade-in {
    animation: fade-in var(--transition-normal) ease-out;
}

.animate-fade-out {
    animation: fade-out var(--transition-normal) ease-out;
}

.animate-slide-in-up {
    animation: slide-in-up var(--transition-normal) ease-out;
}

.animate-slide-in-down {
    animation: slide-in-down var(--transition-normal) ease-out;
}

.animate-slide-in-left {
    animation: slide-in-left var(--transition-normal) ease-out;
}

.animate-slide-in-right {
    animation: slide-in-right var(--transition-normal) ease-out;
}

.animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate 1s linear infinite;
}

.animate-wobble {
    animation: wobble 1s ease-in-out;
}

/* ===== HOVER ANIMATIONS ===== */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .animate-fade-in,
    .animate-fade-out,
    .animate-slide-in-up,
    .animate-slide-in-down,
    .animate-slide-in-left,
    .animate-slide-in-right,
    .animate-bounce-in,
    .animate-shake,
    .animate-pulse,
    .animate-rotate,
    .animate-wobble {
        animation: none !important;
    }
    
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover {
        transform: none !important;
    }
}
