/**
 * Base Styles
 * 
 * Reset, typography, and foundational styles for the Bomberman game.
 */

/* ===== RESET AND BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    line-height: 1.2;
}

p {
    line-height: 1.4;
    margin: 0;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus styles for keyboard navigation */
*:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.flex {
    display: flex;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-column {
    flex-direction: column;
}

.gap-small {
    gap: 10px;
}

.gap-medium {
    gap: 20px;
}

.gap-large {
    gap: 30px;
}

.margin-bottom-small {
    margin-bottom: 10px;
}

.margin-bottom-medium {
    margin-bottom: 20px;
}

.margin-bottom-large {
    margin-bottom: 30px;
}

.padding-small {
    padding: 10px;
}

.padding-medium {
    padding: 20px;
}

.padding-large {
    padding: 30px;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */
:root {
    --breakpoint-mobile: 480px;
    --breakpoint-tablet: 768px;
    --breakpoint-desktop: 1024px;
    --breakpoint-large: 1200px;
}

/* ===== COLOR VARIABLES ===== */
:root {
    --color-primary: #3498db;
    --color-primary-dark: #2980b9;
    --color-secondary: #e74c3c;
    --color-secondary-dark: #c0392b;
    --color-success: #27ae60;
    --color-success-dark: #229954;
    --color-warning: #f39c12;
    --color-warning-dark: #e67e22;
    --color-info: #9b59b6;
    --color-info-dark: #8e44ad;
    --color-light: #ecf0f1;
    --color-dark: #2c3e50;
    --color-gray: #95a5a6;
    --color-gray-dark: #7f8c8d;
}

/* ===== SHADOW VARIABLES ===== */
:root {
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-extra-large: 0 16px 32px rgba(0, 0, 0, 0.25);
}

/* ===== BORDER RADIUS VARIABLES ===== */
:root {
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 12px;
    --radius-extra-large: 16px;
    --radius-round: 50%;
}

/* ===== TRANSITION VARIABLES ===== */
:root {
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}
