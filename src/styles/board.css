/**
 * Game Board Styles
 * 
 * Styles for the game board, tiles, and basic tile states.
 */

/* ===== GAME BOARD ===== */
.board {
    display: grid;
    grid-template-rows: repeat(13, 1fr);
    grid-template-columns: repeat(15, 1fr);
    gap: 1px;
    background: #34495e;
    border: 3px solid var(--color-dark);
    border-radius: var(--radius-medium);
    padding: 8px;
    box-shadow: var(--shadow-large);
    /* Responsive sizing */
    width: min(90vw, 750px);
    height: min(78vw, 650px);
    max-width: 750px;
    max-height: 650px;
}

.board.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

/* ===== TILE CONTAINER ===== */
.tile-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* ===== TILE STYLES ===== */
.tile {
    position: relative;
    border-radius: 2px;
    transition: all var(--transition-fast);
    cursor: pointer;
    overflow: hidden;
}

.tile:hover {
    transform: scale(1.05);
    z-index: 10;
}

.tile-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* ===== WALL TILES ===== */
.tile-wall {
    background: linear-gradient(135deg, var(--color-gray) 0%, var(--color-gray-dark) 100%);
    border: 1px solid #6c7b7d;
}

/* Indestructible walls (structural/border walls) */
.wall-content {
    background: repeating-linear-gradient(
        45deg,
        #bdc3c7,
        #bdc3c7 2px,
        var(--color-gray) 2px,
        var(--color-gray) 4px
    );
    border-radius: 1px;
}

/* Destructible walls */
.tile-wall.destructible {
    background: linear-gradient(135deg, #d35400 0%, var(--color-warning-dark) 100%);
    border: 1px solid #a0522d;
}

.tile-wall.destructible .wall-content {
    background: repeating-linear-gradient(
        45deg,
        var(--color-warning),
        var(--color-warning) 3px,
        var(--color-warning-dark) 3px,
        var(--color-warning-dark) 6px
    );
    border-radius: 2px;
    position: relative;
}

/* Add a subtle texture to destructible walls */
.tile-wall.destructible .wall-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 1px, transparent 1px),
                radial-gradient(circle at 70% 70%, rgba(0,0,0,0.2) 1px, transparent 1px);
    background-size: 8px 8px;
}

.tile-wall:hover {
    transform: none; /* Walls don't scale on hover */
    cursor: default;
}

/* Destructible walls can be hovered for interaction */
.tile-wall.destructible:hover {
    background: linear-gradient(135deg, var(--color-warning-dark) 0%, var(--color-warning) 100%);
    cursor: pointer;
}

/* ===== GROUND TILES ===== */
.tile-ground {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    position: relative;
}

.ground-content {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    position: relative;
}

/* Add a subtle grid pattern to ground tiles */
.ground-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(180deg, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 8px 8px;
}

.tile-ground:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: var(--color-primary);
}

/* ===== PLAYER SPAWN AREAS ===== */
.tile-row-1.tile-col-1,
.tile-row-1.tile-col-13,
.tile-row-11.tile-col-1,
.tile-row-11.tile-col-13 {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 2px solid var(--color-success);
    position: relative;
}

.tile-row-1.tile-col-1::after,
.tile-row-1.tile-col-13::after,
.tile-row-11.tile-col-1::after,
.tile-row-11.tile-col-13::after {
    content: '👤';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    opacity: 0.6;
}

/* ===== TILE STATES ===== */
.tile-has-player {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    border: 2px solid #1976d2;
}

.tile-has-bomb {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    animation: bomb-pulse 1s infinite;
}

.tile-has-explosion {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    animation: explosion-flash 0.5s ease-out;
}

.tile-has-pickup {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
    animation: pickup-glow 2s infinite;
}

/* ===== RESPONSIVE BOARD DESIGN ===== */
@media (max-width: 768px) {
    .board {
        width: 95vw;
        height: 82vw;
        max-width: 600px;
        max-height: 520px;
    }
}

@media (max-width: 480px) {
    .board {
        width: 98vw;
        height: 85vw;
        max-width: 450px;
        max-height: 390px;
    }
}

/* ===== BASIC ANIMATIONS ===== */
@keyframes bomb-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes explosion-flash {
    0% { background: #f1c40f; transform: scale(1.2); }
    100% { background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%); transform: scale(1); }
}

@keyframes pickup-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(155, 89, 182, 0.5); }
    50% { box-shadow: 0 0 15px rgba(155, 89, 182, 0.8); }
}
