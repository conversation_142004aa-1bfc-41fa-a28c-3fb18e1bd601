/**
 * Component Styles
 * 
 * Reusable UI components like buttons, cards, badges, etc.
 */

/* ===== BUTTONS ===== */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    line-height: 1;
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-small);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Button variants */
.btn-primary {
    background: var(--color-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--color-primary-dark);
}

.btn-secondary {
    background: var(--color-secondary);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: var(--color-secondary-dark);
}

.btn-success {
    background: var(--color-success);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: var(--color-success-dark);
}

.btn-warning {
    background: var(--color-warning);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: var(--color-warning-dark);
}

.btn-info {
    background: var(--color-info);
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: var(--color-info-dark);
}

.btn-light {
    background: var(--color-light);
    color: var(--color-dark);
}

.btn-light:hover:not(:disabled) {
    background: #d5dbdb;
}

.btn-dark {
    background: var(--color-dark);
    color: white;
}

.btn-dark:hover:not(:disabled) {
    background: #1a252f;
}

.btn-gray {
    background: var(--color-gray);
    color: white;
}

.btn-gray:hover:not(:disabled) {
    background: var(--color-gray-dark);
}

/* Button sizes */
.btn-small {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-large {
    padding: 14px 28px;
    font-size: 1.125rem;
}

.btn-extra-large {
    padding: 18px 36px;
    font-size: 1.25rem;
}

/* Button shapes */
.btn-round {
    border-radius: var(--radius-round);
    width: 40px;
    height: 40px;
    padding: 0;
}

.btn-round.btn-large {
    width: 50px;
    height: 50px;
}

/* Button states */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: var(--radius-round);
    animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* ===== CARDS ===== */
.card {
    background: white;
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-small);
    overflow: hidden;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-medium);
}

.card-header {
    padding: var(--padding-medium);
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.card-body {
    padding: var(--padding-medium);
}

.card-footer {
    padding: var(--padding-medium);
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

/* ===== BADGES ===== */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: var(--radius-small);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--color-primary);
    color: white;
}

.badge-secondary {
    background: var(--color-secondary);
    color: white;
}

.badge-success {
    background: var(--color-success);
    color: white;
}

.badge-warning {
    background: var(--color-warning);
    color: white;
}

.badge-info {
    background: var(--color-info);
    color: white;
}

.badge-light {
    background: var(--color-light);
    color: var(--color-dark);
}

.badge-dark {
    background: var(--color-dark);
    color: white;
}

/* ===== PROGRESS BARS ===== */
.progress {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: var(--radius-small);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--color-primary);
    border-radius: var(--radius-small);
    transition: width var(--transition-normal);
}

.progress-bar-success {
    background: var(--color-success);
}

.progress-bar-warning {
    background: var(--color-warning);
}

.progress-bar-danger {
    background: var(--color-secondary);
}

.progress-bar-animated {
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem;
    }
}

/* ===== TOOLTIPS ===== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-dark);
    color: white;
    padding: 8px 12px;
    border-radius: var(--radius-small);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--color-dark);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

/* ===== ALERTS ===== */
.alert {
    padding: 12px 16px;
    border-radius: var(--radius-medium);
    margin-bottom: var(--margin-bottom-medium);
    border: 1px solid transparent;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-color: var(--color-success);
    color: var(--color-success-dark);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-color: var(--color-warning);
    color: var(--color-warning-dark);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-color: var(--color-secondary);
    color: var(--color-secondary-dark);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--color-primary);
    color: var(--color-primary-dark);
}
