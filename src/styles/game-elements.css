/**
 * Game Elements Styles
 * 
 * Styles for players, bombs, explosions, and pickups.
 */

/* ===== BASE GAME ELEMENT ===== */
.game-element {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* ===== PLAYER RENDERING ===== */
.player {
    z-index: 4;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-body {
    width: 80%;
    height: 80%;
    border-radius: var(--radius-round);
    position: relative;
    transition: all var(--transition-fast);
}

.player-face {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 40%;
    border-radius: var(--radius-round);
    background: #fff;
    z-index: 1;
}

.player-shadow {
    position: absolute;
    bottom: -5%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 20%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-round);
    z-index: -1;
}

/* ===== PLAYER COLORS ===== */
.player-1 .player-body {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    border: 3px solid #1976d2;
}

.player-2 .player-body {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    border: 3px solid #d32f2f;
}

.player-3 .player-body {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    border: 3px solid #f57c00;
}

.player-4 .player-body {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
    border: 3px solid #7b1fa2;
}

/* ===== PLAYER STATES ===== */
.player.moving .player-body {
    animation: player-walk 0.4s infinite;
}

.player.dead .player-body {
    opacity: 0.5;
    filter: grayscale(100%);
    animation: player-death 1s ease-out;
}

/* ===== PLAYER DIRECTION INDICATORS ===== */
.player.facing-up .player-face::before {
    content: '👆';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
}

.player.facing-down .player-face::before {
    content: '👇';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
}

.player.facing-left .player-face::before {
    content: '👈';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
}

.player.facing-right .player-face::before {
    content: '👉';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
}

/* ===== BOMB RENDERING ===== */
.bomb {
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bomb-body {
    width: 70%;
    height: 70%;
    background: linear-gradient(135deg, var(--color-dark) 0%, #34495e 100%);
    border-radius: var(--radius-round);
    border: 2px solid #1a252f;
    position: relative;
    animation: bomb-pulse 1s infinite;
}

.bomb-fuse {
    position: absolute;
    top: -15%;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 20%;
    background: linear-gradient(to top, var(--color-warning-dark), var(--color-warning));
    border-radius: 2px;
    animation: fuse-burn 3s linear;
}

.bomb-timer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    z-index: 1;
}

.bomb-shadow {
    position: absolute;
    bottom: -10%;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 25%;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius-round);
    z-index: -1;
}

/* ===== BOMB STATES ===== */
.bomb.critical .bomb-body {
    animation: bomb-critical 0.2s infinite;
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.bomb.critical .bomb-fuse {
    animation: fuse-critical 0.1s infinite;
}

/* ===== EXPLOSION RENDERING ===== */
.explosion {
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: explosion-appear 0.5s ease-out;
}

.explosion-core {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #fff 0%, var(--color-warning) 30%, var(--color-secondary) 70%, var(--color-secondary-dark) 100%);
    border-radius: var(--radius-round);
    position: relative;
    animation: explosion-pulse 0.3s infinite;
}

.explosion-flames {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 40%, var(--color-warning) 60%, transparent 80%);
    border-radius: var(--radius-round);
    animation: flames-flicker 0.1s infinite;
}

.explosion-sparks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, #fff 2px, transparent 2px),
        radial-gradient(circle at 80% 30%, #fff 1px, transparent 1px),
        radial-gradient(circle at 30% 80%, #fff 1px, transparent 1px),
        radial-gradient(circle at 70% 70%, #fff 2px, transparent 2px);
    animation: sparks-twinkle 0.2s infinite;
}

/* ===== EXPLOSION TYPES ===== */
.explosion-horizontal .explosion-core {
    width: 100%;
    height: 60%;
    border-radius: 30px;
}

.explosion-vertical .explosion-core {
    width: 60%;
    height: 100%;
    border-radius: 30px;
}

.explosion-end .explosion-core {
    width: 80%;
    height: 80%;
}

/* ===== PICKUP RENDERING ===== */
.pickup {
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pickup-float 2s ease-in-out infinite;
}

.pickup-icon {
    width: 60%;
    height: 60%;
    border-radius: 20%;
    position: relative;
    border: 2px solid #fff;
    box-shadow: var(--shadow-medium);
}

.pickup-glow {
    position: absolute;
    top: -10%;
    left: -10%;
    width: 120%;
    height: 120%;
    border-radius: var(--radius-round);
    background: radial-gradient(circle, transparent 60%, rgba(255, 255, 255, 0.3) 80%, transparent 100%);
    animation: pickup-glow-animation 1.5s ease-in-out infinite;
}

.pickup-shadow {
    position: absolute;
    bottom: -15%;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 20%;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-round);
    z-index: -1;
}

/* ===== PICKUP TYPES ===== */
.pickup-bomb-up .pickup-icon {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.pickup-bomb-up .pickup-icon::before {
    content: '💣';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
}

.pickup-fire-up .pickup-icon {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.pickup-fire-up .pickup-icon::before {
    content: '🔥';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
}

.pickup-speed-up .pickup-icon {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
}

.pickup-speed-up .pickup-icon::before {
    content: '⚡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
}

.pickup-kick .pickup-icon {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
}

.pickup-kick .pickup-icon::before {
    content: '👟';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
}
