/**
 * Layout Styles
 * 
 * Game container, header, footer, and main layout components.
 */

/* ===== GAME CONTAINER ===== */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-extra-large);
    padding: var(--padding-medium);
    max-width: 1200px;
    width: 100%;
    margin: var(--padding-medium);
}

/* ===== GAME HEADER ===== */
.game-header {
    text-align: center;
    margin-bottom: var(--margin-bottom-medium);
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.game-title {
    font-size: 2.5rem;
    color: var(--color-dark);
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-controls {
    display: flex;
    gap: var(--gap-small);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== GAME INFO PANEL ===== */
.game-info {
    display: flex;
    justify-content: center;
    gap: var(--gap-large);
    margin-bottom: var(--margin-bottom-medium);
    padding: 15px;
    background: #f8f9fa;
    border-radius: var(--radius-medium);
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
}

.info-label {
    font-weight: bold;
    color: var(--color-dark);
}

.info-value {
    color: var(--color-secondary);
    font-weight: bold;
}

/* ===== GAME MAIN AREA ===== */
.game-main {
    display: flex;
    justify-content: center;
    margin-bottom: var(--margin-bottom-medium);
}

.board-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* ===== GAME FOOTER ===== */
.game-footer {
    text-align: center;
    padding-top: 15px;
    border-top: 2px solid #e0e0e0;
}

.instructions h3 {
    color: var(--color-dark);
    margin-bottom: var(--margin-bottom-small);
}

.instructions p {
    margin: 5px 0;
    color: var(--color-gray-dark);
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .game-container {
        margin: var(--padding-small);
        padding: 15px;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .game-controls {
        gap: 8px;
    }
    
    .game-info {
        gap: 15px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-info {
        flex-direction: column;
        gap: var(--gap-small);
    }
    
    .instructions p {
        font-size: 0.8rem;
    }
}

/* ===== LOADING STATES ===== */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-message {
    font-size: 1.2rem;
    color: var(--color-gray-dark);
    animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* ===== ERROR STATES ===== */
.error-container {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid var(--color-secondary);
    border-radius: var(--radius-medium);
    padding: var(--padding-medium);
    margin: var(--padding-medium);
    text-align: center;
    color: var(--color-secondary);
}

.error-container h2 {
    margin-bottom: var(--margin-bottom-small);
}

.error-container p {
    margin-bottom: var(--margin-bottom-small);
}

/* ===== MODAL/OVERLAY STYLES ===== */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: var(--radius-large);
    padding: var(--padding-large);
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-extra-large);
    animation: modal-appear var(--transition-normal);
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ===== GRID LAYOUTS ===== */
.grid {
    display: grid;
    gap: var(--gap-medium);
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}
