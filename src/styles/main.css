/**
 * Main Stylesheet
 * 
 * Imports all modular CSS files for the Bomberman game.
 * This file serves as the single entry point for all styles.
 */

/* ===== IMPORT ORDER ===== */
/* 1. Base styles (reset, variables, typography) */
@import './base.css';

/* 2. Layout components (containers, grid, responsive) */
@import './layout.css';

/* 3. UI components (buttons, cards, forms) */
@import './components.css';

/* 4. Game board and tiles */
@import './board.css';

/* 5. Game elements (players, bombs, explosions, pickups) */
@import './game-elements.css';

/* 6. Animations (all keyframes and transitions) */
@import './animations.css';

/**
 * CSS Architecture Notes:
 * 
 * base.css - Foundation styles, CSS variables, reset, typography
 * layout.css - Page layout, containers, responsive grid systems
 * components.css - Reusable UI components (buttons, cards, etc.)
 * board.css - Game board, tiles, and basic tile states
 * game-elements.css - Players, bombs, explosions, pickups rendering
 * animations.css - All CSS animations and transitions
 * 
 * This modular approach provides:
 * - Better maintainability
 * - Easier debugging
 * - Cleaner separation of concerns
 * - Reusable components
 * - Consistent naming conventions
 */
