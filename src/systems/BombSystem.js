/**
 * Bomb System Module
 * 
 * Handles bomb placement, countdown, explosion logic, and chain reactions.
 */

export class BombSystem {
    constructor(boardComponent, gameState) {
        this.board = boardComponent;
        this.gameState = gameState;
        this.activeBombs = new Map();
        this.nextBombId = 1;
        
        console.log('Bomb System initialized');
    }

    /**
     * Place a bomb at the specified position
     */
    placeBomb(row, col, playerId, power = 1, timer = 3000) {
        if (!this.board.isValidPosition(row, col)) {
            return false;
        }

        const tile = this.board.getTileAt(row, col);
        if (!tile || tile.type === 'wall' || tile.hasBomb) {
            return false;
        }

        const bombId = this.nextBombId++;
        const bomb = {
            id: bombId,
            row: row,
            col: col,
            playerId: playerId,
            power: power,
            timer: timer,
            timeLeft: timer / 1000, // Convert to seconds for display
            startTime: Date.now()
        };

        // Add bomb to active bombs
        this.activeBombs.set(bombId, bomb);

        // Update tile with bomb
        this.board.updateTileAt(row, col, {
            hasBomb: true,
            bombData: {
                id: bombId,
                timeLeft: bomb.timeLeft,
                power: bomb.power,
                playerId: bomb.playerId
            }
        });

        console.log(`Bomb ${bombId} placed at (${row}, ${col}) by player ${playerId}`);
        return bombId;
    }

    /**
     * Update all active bombs (called from game loop)
     */
    updateBombs(deltaTime) {
        let bombsExploded = false;

        for (const [bombId, bomb] of this.activeBombs) {
            // Update timer
            bomb.timer -= deltaTime;
            bomb.timeLeft = Math.max(0, bomb.timer / 1000);

            // Update tile display
            const tile = this.board.getTileAt(bomb.row, bomb.col);
            if (tile && tile.hasBomb && tile.bombData) {
                tile.bombData.timeLeft = bomb.timeLeft;
            }

            // Check if bomb should explode
            if (bomb.timer <= 0) {
                this.explodeBomb(bombId);
                bombsExploded = true;
            }
        }

        return bombsExploded;
    }

    /**
     * Explode a bomb and create explosion pattern
     */
    explodeBomb(bombId) {
        const bomb = this.activeBombs.get(bombId);
        if (!bomb) return;

        const { row, col, power, playerId } = bomb;

        // Remove bomb from active bombs
        this.activeBombs.delete(bombId);

        // Remove bomb from tile
        this.board.updateTileAt(row, col, {
            hasBomb: false,
            bombData: null
        });

        // Create center explosion
        this.createExplosion(row, col, 'center', 'none', power, playerId);

        // Create explosion in all four directions
        const directions = [
            { dr: -1, dc: 0, type: 'vertical', dir: 'up' },    // Up
            { dr: 1, dc: 0, type: 'vertical', dir: 'down' },   // Down
            { dr: 0, dc: -1, type: 'horizontal', dir: 'left' }, // Left
            { dr: 0, dc: 1, type: 'horizontal', dir: 'right' }  // Right
        ];

        directions.forEach(({ dr, dc, type, dir }) => {
            this.createExplosionLine(row, col, dr, dc, power, type, dir, playerId);
        });

        console.log(`Bomb ${bombId} exploded at (${row}, ${col}) with power ${power}`);
    }

    /**
     * Create explosion line in a specific direction
     */
    createExplosionLine(startRow, startCol, deltaRow, deltaCol, power, type, direction, playerId) {
        for (let i = 1; i <= power; i++) {
            const newRow = startRow + (deltaRow * i);
            const newCol = startCol + (deltaCol * i);

            if (!this.board.isValidPosition(newRow, newCol)) {
                break;
            }

            const tile = this.board.getTileAt(newRow, newCol);
            if (!tile) break;

            // Stop at indestructible walls
            if (tile.type === 'wall' && !tile.isDestructible) {
                break;
            }

            // Destroy destructible walls
            if (tile.type === 'wall' && tile.isDestructible) {
                this.destroyWall(newRow, newCol, playerId);
                this.createExplosion(newRow, newCol, 'end', direction, 1, playerId);
                break; // Stop explosion after destroying wall
            }

            // Create explosion on ground tiles
            const explosionType = (i === power) ? 'end' : type;
            this.createExplosion(newRow, newCol, explosionType, direction, 1, playerId);

            // Chain reaction with other bombs
            if (tile.hasBomb && tile.bombData) {
                // Delay chain reaction slightly for visual effect
                setTimeout(() => {
                    this.explodeBomb(tile.bombData.id);
                }, 100);
            }

            // Damage players (future implementation)
            if (tile.hasPlayer) {
                this.damagePlayer(newRow, newCol, playerId);
            }
        }
    }

    /**
     * Create an explosion at a specific position
     */
    createExplosion(row, col, type, direction, intensity, playerId, duration = 1000) {
        this.board.updateTileAt(row, col, {
            hasExplosion: true,
            explosionData: {
                type: type,
                direction: direction,
                intensity: intensity,
                playerId: playerId,
                duration: duration,
                startTime: Date.now()
            }
        });
    }

    /**
     * Destroy a destructible wall
     */
    destroyWall(row, col, playerId) {
        // Convert wall to ground
        this.board.updateTileAt(row, col, {
            type: 'ground',
            isDestructible: false
        });

        // Add score for destroying wall
        this.gameState.addScore(10);

        // Chance to spawn pickup
        if (Math.random() < 0.3) { // 30% chance
            this.spawnPickup(row, col);
        }

        console.log(`Wall destroyed at (${row}, ${col}) by player ${playerId}`);
    }

    /**
     * Spawn a random pickup at the specified position
     */
    spawnPickup(row, col) {
        const pickupTypes = ['bomb-up', 'fire-up', 'speed-up', 'kick'];
        const randomType = pickupTypes[Math.floor(Math.random() * pickupTypes.length)];

        this.board.updateTileAt(row, col, {
            hasPickup: true,
            pickupData: {
                type: randomType,
                value: 1,
                spawnTime: Date.now()
            }
        });

        console.log(`Pickup ${randomType} spawned at (${row}, ${col})`);
    }

    /**
     * Damage a player (placeholder for future implementation)
     */
    damagePlayer(row, col, bombPlayerId) {
        const tile = this.board.getTileAt(row, col);
        if (tile && tile.hasPlayer && tile.playerData) {
            const playerId = tile.playerData.id;
            
            // Don't damage the player who placed the bomb (for a short time)
            if (playerId === bombPlayerId) {
                // Could implement temporary immunity here
                return;
            }

            console.log(`Player ${playerId} damaged by explosion at (${row}, ${col})`);
            
            // Future: Implement player death, respawn, etc.
            this.board.updateTileAt(row, col, {
                hasPlayer: false,
                playerData: null
            });

            this.gameState.loseLife();
        }
    }

    /**
     * Update explosions (remove expired ones)
     */
    updateExplosions(deltaTime) {
        let explosionsRemoved = false;

        for (let row = 0; row < 13; row++) {
            for (let col = 0; col < 15; col++) {
                const tile = this.board.getTileAt(row, col);
                
                if (tile && tile.hasExplosion && tile.explosionData) {
                    tile.explosionData.duration -= deltaTime;
                    
                    if (tile.explosionData.duration <= 0) {
                        this.board.updateTileAt(row, col, {
                            hasExplosion: false,
                            explosionData: null
                        });
                        explosionsRemoved = true;
                    }
                }
            }
        }

        return explosionsRemoved;
    }

    /**
     * Get all active bombs
     */
    getActiveBombs() {
        return Array.from(this.activeBombs.values());
    }

    /**
     * Get bomb count for a specific player
     */
    getPlayerBombCount(playerId) {
        let count = 0;
        for (const bomb of this.activeBombs.values()) {
            if (bomb.playerId === playerId) {
                count++;
            }
        }
        return count;
    }

    /**
     * Check if a player can place a bomb (based on bomb limit)
     */
    canPlayerPlaceBomb(playerId, maxBombs = 1) {
        return this.getPlayerBombCount(playerId) < maxBombs;
    }

    /**
     * Remove all bombs (for game reset)
     */
    clearAllBombs() {
        this.activeBombs.clear();
        this.nextBombId = 1;
        
        // Clear bombs from board
        for (let row = 0; row < 13; row++) {
            for (let col = 0; col < 15; col++) {
                const tile = this.board.getTileAt(row, col);
                if (tile && tile.hasBomb) {
                    this.board.updateTileAt(row, col, {
                        hasBomb: false,
                        bombData: null
                    });
                }
            }
        }

        console.log('All bombs cleared');
    }

    /**
     * Get system statistics
     */
    getStats() {
        return {
            activeBombs: this.activeBombs.size,
            nextBombId: this.nextBombId
        };
    }
}
