<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Framework Test</title>
    <style>
        .test-board {
            display: grid;
            grid-template-columns: repeat(5, 50px);
            grid-template-rows: repeat(5, 50px);
            gap: 1px;
            background: #333;
            padding: 10px;
        }
        .test-tile {
            background: #4CAF50;
            border: 1px solid #333;
        }
        .test-tile.wall {
            background: #666;
        }
    </style>
</head>
<body>
    <h1>Framework Test</h1>
    <div id="test-app"></div>

    <script type="module">
        import { defineComponent, h } from './mini-framework/src/framework.js';

        const TestBoard = defineComponent({
            render() {
                console.log('TestBoard render called');
                
                const tiles = [];
                for (let i = 0; i < 25; i++) {
                    const row = Math.floor(i / 5);
                    const col = i % 5;
                    const isWall = (row % 2 === 0) && (col % 2 === 0);
                    
                    tiles.push(
                        h('div', {
                            class: `test-tile ${isWall ? 'wall' : ''}`,
                            key: `tile-${i}`
                        }, [])
                    );
                }
                
                return h('div', { class: 'test-board' }, tiles);
            }
        });

        // Test the component
        const testApp = document.getElementById('test-app');
        const testBoard = new TestBoard();
        testBoard.mount(testApp);
        
        console.log('Test board mounted');
    </script>
</body>
</html>
